syntax = "v1"

info (
    title:   "变量管理API"
    desc:    "变量的创建、查询、更新、删除等管理功能"
    author:  "auth-system"
    email:   "<EMAIL>"
    version: "v1.0.0"
)

import (
    "types/types.api"
    "types/amis.api"
)

// 创建变量请求
type CreateVariableRequest {
    Name        string `json:"name"        validate:"required,min=1,max=100"`
    Value       string `json:"value"       validate:"required"`
    Description string `json:"description" validate:"max=1000"`
    Type        string `json:"type"        validate:"required,oneof=string number boolean json"`
    Status      string `json:"status"      validate:"oneof=active inactive"`
    AppId       uint   `json:"appId"       validate:"required"`
}

// 更新变量请求
type UpdateVariableRequest {
    Name        string `json:"name"        validate:"required,min=1,max=100"`
    Value       string `json:"value"       validate:"required"`
    Description string `json:"description" validate:"max=1000"`
    Type        string `json:"type"        validate:"required,oneof=string number boolean json"`
    Status      string `json:"status"      validate:"oneof=active inactive"`
}

// 更新变量状态请求
type UpdateVariableStatusRequest {
    Status string `json:"status" validate:"required,oneof=active inactive"`
}

// 更新变量值请求
type UpdateVariableValueRequest {
    Value string `json:"value" validate:"required"`
}

// 变量列表查询请求
type VariableListRequest {
    PageRequest
    Name        string `json:"name"        validate:"max=100"`   // 变量名称过滤
    Type        string `json:"type"        validate:"oneof=string number boolean json"` // 类型过滤
    Status      string `json:"status"      validate:"oneof=active inactive"` // 状态过滤
    AppId       uint   `json:"appId"`                          // 应用ID过滤
}

// 变量值验证请求
type VariableValueValidateRequest {
    Value string `json:"value" validate:"required"`
    Type  string `json:"type"  validate:"required,oneof=string number boolean json"`
}

// 变量值验证响应
type VariableValueValidateResponse {
    Valid   bool        `json:"valid"`
    Message string      `json:"message"`
    Value   interface{} `json:"value,omitempty"`
}

// 变量详情响应
type VariableDetailResponse {
    VariableInfo
    AppName     string `json:"appName"`     // 所属应用名称
    UserCount   int    `json:"userCount"`   // 关联用户数量
}

// 变量统计信息
type VariableStats {
    Id          uint   `json:"id"`
    Name        string `json:"name"`
    Type        string `json:"type"`
    Status      string `json:"status"`
    AppName     string `json:"appName"`
    UserCount   int    `json:"userCount"`
    CreatedAt   string `json:"createdAt"`
    UpdatedAt   string `json:"updatedAt"`
}

// 批量变量操作请求
type BatchVariableRequest {
    VariableIds []uint `json:"variableIds" validate:"min=1,max=1000"` // 变量ID列表
}

// 批量变量更新请求
type BatchVariableUpdateRequest {
    Variables []UpdateVariableRequest `json:"variables" validate:"min=1,max=100"` // 变量更新列表
}

@server (
    group: variable
    prefix: /api/v1
)
service variable {
    @doc (
        summary: "创建变量"
        description: "在指定应用下创建新变量"
    )
    @handler CreateVariableHandler
    post /tenants/:tenantId/apps/:appId/variables (CreateVariableRequest) returns (AmisResponse)

    @doc (
        summary: "获取变量详情"
        description: "根据变量ID获取变量详细信息"
    )
    @handler GetVariableHandler
    get /tenants/:tenantId/apps/:appId/variables/:id (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "更新变量信息"
        description: "更新变量的基本信息"
    )
    @handler UpdateVariableHandler
    put /tenants/:tenantId/apps/:appId/variables/:id (UpdateVariableRequest) returns (AmisResponse)

    @doc (
        summary: "删除变量"
        description: "删除指定变量"
    )
    @handler DeleteVariableHandler
    delete /tenants/:tenantId/apps/:appId/variables/:id (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "获取变量列表"
        description: "分页获取变量列表，支持名称、类型、状态过滤"
    )
    @handler ListVariablesHandler
    get /tenants/:tenantId/apps/:appId/variables (VariableListRequest) returns (AmisResponse)

    @doc (
        summary: "获取租户变量列表"
        description: "获取租户下所有应用的变量列表"
    )
    @handler ListTenantVariablesHandler
    get /tenants/:tenantId/variables (VariableListRequest) returns (AmisResponse)

    @doc (
        summary: "更新变量状态"
        description: "更新变量状态（启用/禁用）"
    )
    @handler UpdateVariableStatusHandler
    put /tenants/:tenantId/apps/:appId/variables/:id/status (UpdateVariableStatusRequest) returns (AmisResponse)

    @doc (
        summary: "更新变量值"
        description: "更新变量的值"
    )
    @handler UpdateVariableValueHandler
    put /tenants/:tenantId/apps/:appId/variables/:id/value (UpdateVariableValueRequest) returns (AmisResponse)

    @doc (
        summary: "获取变量值"
        description: "获取变量的当前值"
    )
    @handler GetVariableValueHandler
    get /tenants/:tenantId/apps/:appId/variables/:id/value (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "验证变量值"
        description: "验证变量值是否符合指定类型"
    )
    @handler ValidateVariableValueHandler
    post /tenants/:tenantId/apps/:appId/variables/validate-value (VariableValueValidateRequest) returns (AmisResponse)

    @doc (
        summary: "获取变量统计信息"
        description: "获取变量的统计信息"
    )
    @handler GetVariableStatsHandler
    get /tenants/:tenantId/apps/:appId/variables/:id/stats (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "检查变量名称可用性"
        description: "检查变量名称是否已被使用"
    )
    @handler CheckVariableNameHandler
    get /tenants/:tenantId/apps/:appId/variables/check-name/:name (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "获取变量用户列表"
        description: "获取变量关联的用户列表"
    )
    @handler GetVariableUsersHandler
    get /tenants/:tenantId/apps/:appId/variables/:id/users (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "批量删除变量"
        description: "批量删除多个变量"
    )
    @handler BatchDeleteVariablesHandler
    delete /tenants/:tenantId/apps/:appId/variables/batch (BatchVariableRequest) returns (AmisResponse)

    @doc (
        summary: "批量更新变量状态"
        description: "批量更新多个变量的状态"
    )
    @handler BatchUpdateVariableStatusHandler
    put /tenants/:tenantId/apps/:appId/variables/batch-status (UpdateVariableStatusRequest) returns (AmisResponse)

    @doc (
        summary: "批量更新变量"
        description: "批量更新多个变量"
    )
    @handler BatchUpdateVariablesHandler
    put /tenants/:tenantId/apps/:appId/variables/batch (BatchVariableUpdateRequest) returns (AmisResponse)

    @doc (
        summary: "获取变量类型统计"
        description: "获取应用下变量的类型统计"
    )
    @handler GetVariableTypeStatsHandler
    get /tenants/:tenantId/apps/:appId/variables/type-stats (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "搜索变量"
        description: "根据关键词搜索变量"
    )
    @handler SearchVariablesHandler
    get /tenants/:tenantId/apps/:appId/variables/search/:keyword (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "导出变量配置"
        description: "导出应用的变量配置"
    )
    @handler ExportVariablesHandler
    get /tenants/:tenantId/apps/:appId/variables/export (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "导入变量配置"
        description: "导入变量配置"
    )
    @handler ImportVariablesHandler
    post /tenants/:tenantId/apps/:appId/variables/import (AmisResponse) returns (AmisResponse)
}
