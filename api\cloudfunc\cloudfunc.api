syntax = "v1"

info (
    title:   "云函数管理API"
    desc:    "云函数的创建、查询、更新、删除等管理功能"
    author:  "auth-system"
    email:   "<EMAIL>"
    version: "v1.0.0"
)

import (
    "types/types.api"
    "types/amis.api"
)

// 创建云函数请求
type CreateCloudFuncRequest {
    Name        string `json:"name"        validate:"required,min=1,max=100"`
    Description string `json:"description" validate:"max=1000"`
    Code        string `json:"code"        validate:"required"`
    Runtime     string `json:"runtime"     validate:"required,oneof=go1.21 nodejs18 python3.9"`
    Status      string `json:"status"      validate:"oneof=active inactive deploying"`
    AppId       uint   `json:"appId"       validate:"required"`
}

// 更新云函数请求
type UpdateCloudFuncRequest {
    Name        string `json:"name"        validate:"required,min=1,max=100"`
    Description string `json:"description" validate:"max=1000"`
    Code        string `json:"code"        validate:"required"`
    Runtime     string `json:"runtime"     validate:"required,oneof=go1.21 nodejs18 python3.9"`
    Status      string `json:"status"      validate:"oneof=active inactive deploying"`
}

// 更新云函数状态请求
type UpdateCloudFuncStatusRequest {
    Status string `json:"status" validate:"required,oneof=active inactive deploying"`
}

// 更新云函数代码请求
type UpdateCloudFuncCodeRequest {
    Code string `json:"code" validate:"required"`
}

// 云函数列表查询请求
type CloudFuncListRequest {
    PageRequest
    Name        string `json:"name"        validate:"max=100"`   // 云函数名称过滤
    Runtime     string `json:"runtime"     validate:"oneof=go1.21 nodejs18 python3.9"` // 运行时过滤
    Status      string `json:"status"      validate:"oneof=active inactive deploying"` // 状态过滤
    AppId       uint   `json:"appId"`                          // 应用ID过滤
}

// 云函数执行请求
type ExecuteCloudFuncRequest {
    Params interface{} `json:"params"` // 执行参数
}

// 云函数执行响应
type ExecuteCloudFuncResponse {
    Success    bool        `json:"success"`
    Result     interface{} `json:"result"`
    Error      string      `json:"error,omitempty"`
    ExecTime   int64       `json:"execTime"`   // 执行时间（毫秒）
    MemoryUsed int64       `json:"memoryUsed"` // 内存使用（字节）
}

// 云函数部署请求
type DeployCloudFuncRequest {
    Code    string `json:"code"    validate:"required"`
    Runtime string `json:"runtime" validate:"required,oneof=go1.21 nodejs18 python3.9"`
}

// 云函数部署状态
type CloudFuncDeployStatus {
    Id          uint   `json:"id"`
    Name        string `json:"name"`
    Status      string `json:"status"`
    DeployTime   string `json:"deployTime"`
    Logs        string `json:"logs"`
    Error       string `json:"error,omitempty"`
}

// 云函数详情响应
type CloudFuncDetailResponse {
    CloudFuncInfo
    AppName     string `json:"appName"`     // 所属应用名称
    UserCount   int    `json:"userCount"`   // 关联用户数量
    CodeSize    int64  `json:"codeSize"`    // 代码大小（字节）
    LastDeploy  string `json:"lastDeploy"`  // 最后部署时间
}

// 云函数统计信息
type CloudFuncStats {
    Id          uint   `json:"id"`
    Name        string `json:"name"`
    Runtime     string `json:"runtime"`
    Status      string `json:"status"`
    AppName     string `json:"appName"`
    UserCount   int    `json:"userCount"`
    ExecCount   int64  `json:"execCount"`   // 执行次数
    AvgExecTime int64  `json:"avgExecTime"` // 平均执行时间（毫秒）
    CreatedAt   string `json:"createdAt"`
    UpdatedAt   string `json:"updatedAt"`
}

// 批量云函数操作请求
type BatchCloudFuncRequest {
    CloudFuncIds []uint `json:"cloudFuncIds" validate:"min=1,max=100"` // 云函数ID列表
}

// 云函数日志请求
type CloudFuncLogRequest {
    StartTime string `json:"startTime" validate:"datetime"` // 开始时间
    EndTime   string `json:"endTime"   validate:"datetime"` // 结束时间
    Level     string `json:"level"     validate:"oneof=info warn error"` // 日志级别
    Limit     int    `json:"limit"     validate:"min=1,max=1000"` // 限制数量
}

// 云函数日志响应
type CloudFuncLogResponse {
    Logs []CloudFuncLog `json:"logs"`
    Total int64 `json:"total"`
}

// 云函数日志
type CloudFuncLog {
    Id        uint   `json:"id"`
    Timestamp string `json:"timestamp"`
    Level     string `json:"level"`
    Message   string `json:"message"`
    Details   string `json:"details"`
}

@server (
    group: cloudfunc
    prefix: /api/v1
)
service cloudfunc {
    @doc (
        summary: "创建云函数"
        description: "在指定应用下创建新云函数"
    )
    @handler CreateCloudFuncHandler
    post /tenants/:tenantId/apps/:appId/cloudfuncs (CreateCloudFuncRequest) returns (AmisResponse)

    @doc (
        summary: "获取云函数详情"
        description: "根据云函数ID获取云函数详细信息"
    )
    @handler GetCloudFuncHandler
    get /tenants/:tenantId/apps/:appId/cloudfuncs/:id (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "更新云函数信息"
        description: "更新云函数的基本信息"
    )
    @handler UpdateCloudFuncHandler
    put /tenants/:tenantId/apps/:appId/cloudfuncs/:id (UpdateCloudFuncRequest) returns (AmisResponse)

    @doc (
        summary: "删除云函数"
        description: "删除指定云函数"
    )
    @handler DeleteCloudFuncHandler
    delete /tenants/:tenantId/apps/:appId/cloudfuncs/:id (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "获取云函数列表"
        description: "分页获取云函数列表，支持名称、运行时、状态过滤"
    )
    @handler ListCloudFuncsHandler
    get /tenants/:tenantId/apps/:appId/cloudfuncs (CloudFuncListRequest) returns (AmisResponse)

    @doc (
        summary: "获取租户云函数列表"
        description: "获取租户下所有应用的云函数列表"
    )
    @handler ListTenantCloudFuncsHandler
    get /tenants/:tenantId/cloudfuncs (CloudFuncListRequest) returns (AmisResponse)

    @doc (
        summary: "更新云函数状态"
        description: "更新云函数状态（启用/禁用/部署中）"
    )
    @handler UpdateCloudFuncStatusHandler
    put /tenants/:tenantId/apps/:appId/cloudfuncs/:id/status (UpdateCloudFuncStatusRequest) returns (AmisResponse)

    @doc (
        summary: "更新云函数代码"
        description: "更新云函数的代码"
    )
    @handler UpdateCloudFuncCodeHandler
    put /tenants/:tenantId/apps/:appId/cloudfuncs/:id/code (UpdateCloudFuncCodeRequest) returns (AmisResponse)

    @doc (
        summary: "获取云函数代码"
        description: "获取云函数的当前代码"
    )
    @handler GetCloudFuncCodeHandler
    get /tenants/:tenantId/apps/:appId/cloudfuncs/:id/code (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "执行云函数"
        description: "执行指定的云函数"
    )
    @handler ExecuteCloudFuncHandler
    post /tenants/:tenantId/apps/:appId/cloudfuncs/:id/execute (ExecuteCloudFuncRequest) returns (AmisResponse)

    @doc (
        summary: "部署云函数"
        description: "部署云函数到运行环境"
    )
    @handler DeployCloudFuncHandler
    post /tenants/:tenantId/apps/:appId/cloudfuncs/:id/deploy (DeployCloudFuncRequest) returns (AmisResponse)

    @doc (
        summary: "获取部署状态"
        description: "获取云函数的部署状态"
    )
    @handler GetCloudFuncDeployStatusHandler
    get /tenants/:tenantId/apps/:appId/cloudfuncs/:id/deploy-status (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "获取云函数统计信息"
        description: "获取云函数的统计信息"
    )
    @handler GetCloudFuncStatsHandler
    get /tenants/:tenantId/apps/:appId/cloudfuncs/:id/stats (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "检查云函数名称可用性"
        description: "检查云函数名称是否已被使用"
    )
    @handler CheckCloudFuncNameHandler
    get /tenants/:tenantId/apps/:appId/cloudfuncs/check-name/:name (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "获取云函数用户列表"
        description: "获取云函数关联的用户列表"
    )
    @handler GetCloudFuncUsersHandler
    get /tenants/:tenantId/apps/:appId/cloudfuncs/:id/users (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "批量删除云函数"
        description: "批量删除多个云函数"
    )
    @handler BatchDeleteCloudFuncsHandler
    delete /tenants/:tenantId/apps/:appId/cloudfuncs/batch (BatchCloudFuncRequest) returns (AmisResponse)

    @doc (
        summary: "批量更新云函数状态"
        description: "批量更新多个云函数的状态"
    )
    @handler BatchUpdateCloudFuncStatusHandler
    put /tenants/:tenantId/apps/:appId/cloudfuncs/batch-status (UpdateCloudFuncStatusRequest) returns (AmisResponse)

    @doc (
        summary: "获取云函数运行时统计"
        description: "获取应用下云函数的运行时统计"
    )
    @handler GetCloudFuncRuntimeStatsHandler
    get /tenants/:tenantId/apps/:appId/cloudfuncs/runtime-stats (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "搜索云函数"
        description: "根据关键词搜索云函数"
    )
    @handler SearchCloudFuncsHandler
    get /tenants/:tenantId/apps/:appId/cloudfuncs/search/:keyword (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "获取云函数日志"
        description: "获取云函数的执行日志"
    )
    @handler GetCloudFuncLogsHandler
    get /tenants/:tenantId/apps/:appId/cloudfuncs/:id/logs (CloudFuncLogRequest) returns (AmisResponse)

    @doc (
        summary: "导出云函数配置"
        description: "导出应用的云函数配置"
    )
    @handler ExportCloudFuncsHandler
    get /tenants/:tenantId/apps/:appId/cloudfuncs/export (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "导入云函数配置"
        description: "导入云函数配置"
    )
    @handler ImportCloudFuncsHandler
    post /tenants/:tenantId/apps/:appId/cloudfuncs/import (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "测试云函数"
        description: "测试云函数的语法和逻辑"
    )
    @handler TestCloudFuncHandler
    post /tenants/:tenantId/apps/:appId/cloudfuncs/:id/test (ExecuteCloudFuncRequest) returns (AmisResponse)

    @doc (
        summary: "获取云函数版本历史"
        description: "获取云函数的版本历史记录"
    )
    @handler GetCloudFuncVersionsHandler
    get /tenants/:tenantId/apps/:appId/cloudfuncs/:id/versions (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "回滚云函数版本"
        description: "回滚云函数到指定版本"
    )
    @handler RollbackCloudFuncVersionHandler
    post /tenants/:tenantId/apps/:appId/cloudfuncs/:id/rollback (AmisResponse) returns (AmisResponse)
}
