-- SQLite3 主数据库结构 - 用于存储租户信息
-- 数据库: auth_system.db

-- 启用外键约束
PRAGMA foreign_keys = ON;

-- 租户表
CREATE TABLE IF NOT EXISTS tenants (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    created_at DATETIME,
    updated_at DATETIME,
    deleted_at DATETIME,
    name VARCHAR(100) NOT NULL UNIQUE,
    database_name VARCHAR(100) NOT NULL UNIQUE,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    max_apps INTEGER NOT NULL DEFAULT 10,
    max_users INTEGER NOT NULL DEFAULT 100,
    max_variables INTEGER NOT NULL DEFAULT 1000,
    max_cloud_funcs INTEGER NOT NULL DEFAULT 50,
    current_apps INTEGER NOT NULL DEFAULT 0,
    current_users INTEGER NOT NULL DEFAULT 0,
    current_variables INTEGER NOT NULL DEFAULT 0,
    current_cloud_funcs INTEGER NOT NULL DEFAULT 0,
    config TEXT
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_tenants_status ON tenants(status);
CREATE INDEX IF NOT EXISTS idx_tenants_created_at ON tenants(created_at);
CREATE INDEX IF NOT EXISTS idx_tenants_deleted_at ON tenants(deleted_at);

-- 插入示例租户数据
INSERT OR IGNORE INTO tenants (name, database_name, status, max_apps, max_users, max_variables, max_cloud_funcs, config) VALUES
('示例租户', 'tenant_example.db', 'active', 10, 100, 1000, 50, '{"theme": "default", "language": "zh-CN"}'),
('测试租户', 'tenant_test.db', 'active', 5, 50, 500, 25, '{"theme": "dark", "language": "en-US"}'),
('演示租户', 'tenant_demo.db', 'disabled', 3, 30, 300, 15, '{"theme": "light", "language": "zh-CN"}');

-- 创建触发器：自动更新时间戳
CREATE TRIGGER IF NOT EXISTS tenants_update_trigger
AFTER UPDATE ON tenants
FOR EACH ROW
BEGIN
    UPDATE tenants SET updated_at = datetime('now') WHERE id = NEW.id;
END;
