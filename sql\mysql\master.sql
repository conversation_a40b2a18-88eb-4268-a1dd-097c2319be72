-- 主数据库结构 - 用于存储租户信息
-- 数据库: auth_system

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `auth_system` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `auth_system`;

-- 租户表
CREATE TABLE IF NOT EXISTS `tenants` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `created_at` datetime(3) DEFAULT NULL,
    `updated_at` datetime(3) DEFAULT NULL,
    `deleted_at` datetime(3) DEFAULT NULL,
    `name` varchar(100) NOT NULL,
    `database_name` varchar(100) NOT NULL,
    `status` varchar(20) NOT NULL DEFAULT 'active',
    `max_apps` int NOT NULL DEFAULT '10',
    `max_users` int NOT NULL DEFAULT '100',
    `max_variables` int NOT NULL DEFAULT '1000',
    `max_cloud_funcs` int NOT NULL DEFAULT '50',
    `current_apps` int NOT NULL DEFAULT '0',
    `current_users` int NOT NULL DEFAULT '0',
    `current_variables` int NOT NULL DEFAULT '0',
    `current_cloud_funcs` int NOT NULL DEFAULT '0',
    `config` text,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_tenants_name` (`name`),
    UNIQUE KEY `idx_tenants_database_name` (`database_name`),
    KEY `idx_tenants_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='租户信息表';

-- 创建索引
CREATE INDEX idx_tenants_status ON tenants(status);
CREATE INDEX idx_tenants_created_at ON tenants(created_at);

-- 插入示例租户数据
INSERT INTO `tenants` (`name`, `database_name`, `status`, `max_apps`, `max_users`, `max_variables`, `max_cloud_funcs`, `config`) VALUES
('示例租户', 'tenant_example', 'active', 10, 100, 1000, 50, '{"theme": "default", "language": "zh-CN"}'),
('测试租户', 'tenant_test', 'active', 5, 50, 500, 25, '{"theme": "dark", "language": "en-US"}'),
('演示租户', 'tenant_demo', 'disabled', 3, 30, 300, 15, '{"theme": "light", "language": "zh-CN"}');
