syntax = "v1"

info (
    title:   "系统管理API"
    desc:    "系统信息、健康检查、配置管理等系统管理功能"
    author:  "auth-system"
    email:   "<EMAIL>"
    version: "v1.0.0"
)

import (
    "types/types.api"
    "types/amis.api"
)

// 系统信息响应
type SystemInfoResponse {
    SystemInfo `json:",inline"`
}

// 健康检查响应
type HealthCheckResponse {
    HealthCheckData `json:",inline"`
}

@server (
    group: system
    prefix: /api/v1
)
service system {
    @doc (
        summary: "系统信息"
        description: "获取系统基本信息和状态"
    )
    @handler GetSystemInfoHandler
    get /system/info (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "健康检查"
        description: "系统健康状态检查"
    )
    @handler HealthCheckHandler
    get /system/health (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "系统配置"
        description: "获取系统配置信息"
    )
    @handler GetSystemConfigHandler
    get /system/config (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "更新系统配置"
        description: "更新系统配置（需要管理员权限）"
    )
    @handler UpdateSystemConfigHandler
    put /system/config (SystemConfig) returns (AmisResponse)

    @doc (
        summary: "系统统计"
        description: "获取系统统计信息"
    )
    @handler GetSystemStatsHandler
    get /system/stats (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "系统日志"
        description: "获取系统日志"
    )
    @handler GetSystemLogsHandler
    get /system/logs (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "API文档"
        description: "获取API文档信息"
    )
    @handler GetApiDocsHandler
    get /system/api-docs (AmisResponse) returns (AmisResponse)
}
