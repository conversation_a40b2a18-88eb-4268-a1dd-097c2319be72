syntax = "v1"

info (
    title:   "租户管理API"
    desc:    "租户的创建、查询、更新、删除等管理功能"
    author:  "auth-system"
    email:   "<EMAIL>"
    version: "v1.0.0"
)

import (
    "types/types.api"
    "types/amis.api"
)

// 资源使用情况
type ResourceUsage {
    Used  int `json:"used"`   // 已使用数量
    Total int `json:"total"`  // 总数量限制
    Percentage float64 `json:"percentage"` // 使用百分比
}

// 创建租户请求
type CreateTenantRequest {
    Name            string `json:"name"            validate:"required,min=1,max=100"`
    DatabaseName    string `json:"databaseName"    validate:"required,min=1,max=100,alphanum"`
    MaxApps         int    `json:"maxApps"         validate:"min=1,max=1000"`
    MaxUsers        int    `json:"maxUsers"        validate:"min=1,max=10000"`
    MaxVariables    int    `json:"maxVariables"    validate:"min=1,max=100000"`
    MaxCloudFuncs   int    `json:"maxCloudFuncs"   validate:"min=1,max=1000"`
    Config          string `json:"config"`
}

// 更新租户请求
type UpdateTenantRequest {
    Name            string `json:"name"            validate:"required,min=1,max=100"`
    DatabaseName    string `json:"databaseName"    validate:"required,min=1,max=100,alphanum"`
    Status          string `json:"status"          validate:"oneof=active inactive disabled suspended"`
    MaxApps         int    `json:"maxApps"         validate:"min=1,max=1000"`
    MaxUsers        int    `json:"maxUsers"        validate:"min=1,max=10000"`
    MaxVariables    int    `json:"maxVariables"    validate:"min=1,max=100000"`
    MaxCloudFuncs   int    `json:"maxCloudFuncs"   validate:"min=1,max=1000"`
    Config          string `json:"config"`
}

// 更新租户状态请求
type UpdateTenantStatusRequest {
    Status string `json:"status" validate:"required,oneof=active inactive disabled suspended"`
}

// 租户列表查询请求
type TenantListRequest {
    PageRequest
    Name   string `json:"name"   validate:"max=100"`   // 租户名称过滤
    Status string `json:"status" validate:"oneof=active inactive disabled suspended"` // 状态过滤
}

// 租户资源使用情况
type TenantResourceUsage {
    Apps       ResourceUsage `json:"apps"`
    Users      ResourceUsage `json:"users"`
    Variables  ResourceUsage `json:"variables"`
    CloudFuncs ResourceUsage `json:"cloudFuncs"`
}

// 租户详情响应
type TenantDetailResponse {
    TenantInfo
    ResourceUsage TenantResourceUsage `json:"resourceUsage"`
}

@server (
    group: tenant
    prefix: /api/v1
)
service tenant {
    @doc (
        summary: "创建租户"
        description: "创建新的租户，自动创建对应的数据库"
    )
    @handler CreateTenantHandler
    post /tenants (CreateTenantRequest) returns (AmisResponse)

    @doc (
        summary: "获取租户详情"
        description: "根据租户ID获取租户详细信息"
    )
    @handler GetTenantHandler
    get /tenants/:id (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "更新租户信息"
        description: "更新租户的基本信息和资源限制"
    )
    @handler UpdateTenantHandler
    put /tenants/:id (UpdateTenantRequest) returns (AmisResponse)

    @doc (
        summary: "删除租户"
        description: "删除租户及其对应的数据库"
    )
    @handler DeleteTenantHandler
    delete /tenants/:id (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "获取租户列表"
        description: "分页获取租户列表，支持名称和状态过滤"
    )
    @handler ListTenantsHandler
    get /tenants (TenantListRequest) returns (AmisResponse)

    @doc (
        summary: "启用租户"
        description: "将指定租户状态设置为启用"
    )
    @handler EnableTenantHandler
    post /tenants/:id/enable (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "禁用租户"
        description: "将指定租户状态设置为禁用"
    )
    @handler DisableTenantHandler
    post /tenants/:id/disable (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "获取租户统计信息"
        description: "获取租户的资源使用统计信息"
    )
    @handler GetTenantStatsHandler
    get /tenants/:id/stats (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "检查租户名称可用性"
        description: "检查租户名称是否已被使用"
    )
    @handler CheckTenantNameHandler
    get /tenants/check-name/:name (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "检查数据库名称可用性"
        description: "检查数据库名称是否已被使用"
    )
    @handler CheckDatabaseNameHandler
    get /tenants/check-database/:databaseName (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "获取所有租户统计概览"
        description: "获取系统中所有租户的统计概览信息"
    )
    @handler GetAllTenantsStatsHandler
    get /tenants/stats/overview (AmisResponse) returns (AmisResponse)
}
