syntax = "v1"

 
info (
    title:   "通用类型定义"
    desc:    "系统中所有模块共享的通用类型定义"
    author:  "auth-system"
    email:   "<EMAIL>"
    version: "v1.0.0"
)

 

// 用户信息
type UserInfo {
    ID        uint      `json:"id"`
    Username  string    `json:"username"`
    Email     string    `json:"email"`
    FirstName string    `json:"firstName"`
    LastName  string    `json:"lastName"`
    Phone     string    `json:"phone"`
    Status    string    `json:"status"`
    TenantId  uint      `json:"tenantId"`
    CreatedAt interface{} `json:"createdAt"`
    UpdatedAt interface{} `json:"updatedAt"`
}

// 应用信息
type AppInfo {
    ID          uint      `json:"id"`
    Name        string    `json:"name"`
    Description string    `json:"description"`
    AppKey      string    `json:"appKey"`
    AppSecret   string    `json:"appSecret"`
    Status      string    `json:"status"`
    TenantId    uint      `json:"tenantId"`
    CreatedAt   interface{} `json:"createdAt"`
    UpdatedAt   interface{} `json:"updatedAt"`
}

// 变量信息
type VariableInfo {
    ID          uint      `json:"id"`
    Name        string    `json:"name"`
    Description string    `json:"description"`
    Key         string    `json:"key"`
    Value       string    `json:"value"`
    Type        string    `json:"type"`
    Status      string    `json:"status"`
    AppId       uint      `json:"appId"`
    TenantId    uint      `json:"tenantId"`
    CreatedAt   interface{} `json:"createdAt"`
    UpdatedAt   interface{} `json:"updatedAt"`
}

// 云函数信息
type CloudFuncInfo {
    ID          uint      `json:"id"`
    Name        string    `json:"name"`
    Description string    `json:"description"`
    Code        string    `json:"code"`
    Language    string    `json:"language"`
    Status      string    `json:"status"`
    AppId       uint      `json:"appId"`
    TenantId    uint      `json:"tenantId"`
    CreatedAt   interface{} `json:"createdAt"`
    UpdatedAt   interface{} `json:"updatedAt"`
}

// 租户信息
type TenantInfo {
    ID          uint      `json:"id"`
    Name        string    `json:"name"`
    Description string    `json:"description"`
    Domain      string    `json:"domain"`
    Status      string    `json:"status"`
    CreatedAt   interface{} `json:"createdAt"`
    UpdatedAt   interface{} `json:"updatedAt"`
}

// 内存信息
type MemoryInfo {
    Used  uint64 `json:"used"`
    Total uint64 `json:"total"`
}

// 数据库健康状态
type DatabaseHealth {
    Status    string `json:"status"`
    Latency   int64  `json:"latency"`
    Connected bool   `json:"connected"`
}

// 认证配置
type AuthConfig {
    TokenExpireHours   int  `json:"tokenExpireHours"`
    RefreshTokenDays   int  `json:"refreshTokenDays"`
    MaxLoginAttempts   int  `json:"maxLoginAttempts"`
    LockoutMinutes     int  `json:"lockoutMinutes"`
    PasswordMinLength  int  `json:"passwordMinLength"`
    EnableCaptcha      bool `json:"enableCaptcha"`
}

// 租户配置
type TenantConfig {
    MaxAppsPerTenant      int `json:"maxAppsPerTenant"`
    MaxUsersPerTenant     int `json:"maxUsersPerTenant"`
    MaxVariablesPerTenant int `json:"maxVariablesPerTenant"`
    MaxCloudFuncsPerTenant int `json:"maxCloudFuncsPerTenant"`
}

// 安全配置
type SecurityConfig {
    EnableHTTPS       bool     `json:"enableHTTPS"`
    EnableRateLimit   bool     `json:"enableRateLimit"`
    RateLimitRequests int      `json:"rateLimitRequests"`
    RateLimitWindow   int      `json:"rateLimitWindow"`
    EnableAuditLog    bool     `json:"enableAuditLog"`
    AllowedOrigins    []string `json:"allowedOrigins"`
}

// 系统信息
type SystemInfo {
    Version     string     `json:"version"`
    GoVersion   string     `json:"goVersion"`
    Database    string     `json:"database"`
    Environment string     `json:"environment"`
    Uptime      string     `json:"uptime"`
    Memory      MemoryInfo `json:"memory"`
}

// 健康检查响应
type HealthCheckData {
    Status    string            `json:"status"`
    Timestamp string            `json:"timestamp"`
    Services  map[string]string `json:"services"`
    Database  DatabaseHealth    `json:"database"`
}

// 系统配置
type SystemConfig {
    Auth     AuthConfig     `json:"auth"`
    Tenant   TenantConfig   `json:"tenant"`
    Security SecurityConfig `json:"security"`
}
