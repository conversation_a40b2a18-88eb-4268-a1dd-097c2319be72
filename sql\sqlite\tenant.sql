-- SQLite3 租户数据库结构 - 每个租户独立数据库
-- 数据库: tenant_*.db

-- 启用外键约束
PRAGMA foreign_keys = ON;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    created_at DATETIME,
    updated_at DATETIME,
    deleted_at DATETIME,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    phone VARCHAR(20),
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    last_login_at DATETIME,
    tenant_id INTEGER NOT NULL
);

-- 应用表
CREATE TABLE IF NOT EXISTS apps (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    created_at DATETIME,
    updated_at DATETIME,
    deleted_at DATETIME,
    name VARCHA<PERSON>(100) NOT NULL,
    description TEXT,
    app_key VARCHAR(255) NOT NULL UNIQUE,
    app_secret VARCHAR(255) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    tenant_id INTEGER NOT NULL
);

-- 变量表
CREATE TABLE IF NOT EXISTS variables (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    created_at DATETIME,
    updated_at DATETIME,
    deleted_at DATETIME,
    name VARCHAR(100) NOT NULL,
    value TEXT,
    description TEXT,
    type VARCHAR(50) NOT NULL DEFAULT 'string',
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    app_id INTEGER NOT NULL,
    tenant_id INTEGER NOT NULL
);

-- 云函数表
CREATE TABLE IF NOT EXISTS cloud_funcs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    created_at DATETIME,
    updated_at DATETIME,
    deleted_at DATETIME,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    code TEXT,
    runtime VARCHAR(50) NOT NULL DEFAULT 'go1.21',
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    app_id INTEGER NOT NULL,
    tenant_id INTEGER NOT NULL
);

-- 用户应用关联表
CREATE TABLE IF NOT EXISTS user_apps (
    user_id INTEGER NOT NULL,
    app_id INTEGER NOT NULL,
    created_at DATETIME,
    updated_at DATETIME,
    PRIMARY KEY (user_id, app_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (app_id) REFERENCES apps(id) ON DELETE CASCADE
);

-- 用户变量关联表
CREATE TABLE IF NOT EXISTS user_variables (
    user_id INTEGER NOT NULL,
    variable_id INTEGER NOT NULL,
    created_at DATETIME,
    updated_at DATETIME,
    PRIMARY KEY (user_id, variable_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (variable_id) REFERENCES variables(id) ON DELETE CASCADE
);

-- 用户云函数关联表
CREATE TABLE IF NOT EXISTS user_cloudfuncs (
    user_id INTEGER NOT NULL,
    cloud_func_id INTEGER NOT NULL,
    created_at DATETIME,
    updated_at DATETIME,
    PRIMARY KEY (user_id, cloud_func_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (cloud_func_id) REFERENCES cloud_funcs(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);
CREATE INDEX IF NOT EXISTS idx_users_deleted_at ON users(deleted_at);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

CREATE INDEX IF NOT EXISTS idx_apps_status ON apps(status);
CREATE INDEX IF NOT EXISTS idx_apps_tenant_id ON apps(tenant_id);
CREATE INDEX IF NOT EXISTS idx_apps_deleted_at ON apps(deleted_at);
CREATE INDEX IF NOT EXISTS idx_apps_created_at ON apps(created_at);

CREATE INDEX IF NOT EXISTS idx_variables_name ON variables(name);
CREATE INDEX IF NOT EXISTS idx_variables_type ON variables(type);
CREATE INDEX IF NOT EXISTS idx_variables_status ON variables(status);
CREATE INDEX IF NOT EXISTS idx_variables_app_id ON variables(app_id);
CREATE INDEX IF NOT EXISTS idx_variables_tenant_id ON variables(tenant_id);
CREATE INDEX IF NOT EXISTS idx_variables_deleted_at ON variables(deleted_at);
CREATE INDEX IF NOT EXISTS idx_variables_created_at ON variables(created_at);

CREATE INDEX IF NOT EXISTS idx_cloud_funcs_name ON cloud_funcs(name);
CREATE INDEX IF NOT EXISTS idx_cloud_funcs_runtime ON cloud_funcs(runtime);
CREATE INDEX IF NOT EXISTS idx_cloud_funcs_status ON cloud_funcs(status);
CREATE INDEX IF NOT EXISTS idx_cloud_funcs_app_id ON cloud_funcs(app_id);
CREATE INDEX IF NOT EXISTS idx_cloud_funcs_tenant_id ON cloud_funcs(tenant_id);
CREATE INDEX IF NOT EXISTS idx_cloud_funcs_deleted_at ON cloud_funcs(deleted_at);
CREATE INDEX IF NOT EXISTS idx_cloud_funcs_created_at ON cloud_funcs(created_at);

CREATE INDEX IF NOT EXISTS idx_user_apps_app_id ON user_apps(app_id);
CREATE INDEX IF NOT EXISTS idx_user_apps_created_at ON user_apps(created_at);

CREATE INDEX IF NOT EXISTS idx_user_variables_variable_id ON user_variables(variable_id);
CREATE INDEX IF NOT EXISTS idx_user_variables_created_at ON user_variables(created_at);

CREATE INDEX IF NOT EXISTS idx_user_cloudfuncs_cloud_func_id ON user_cloudfuncs(cloud_func_id);
CREATE INDEX IF NOT EXISTS idx_user_cloudfuncs_created_at ON user_cloudfuncs(created_at);

-- 创建触发器：自动更新时间戳
CREATE TRIGGER IF NOT EXISTS users_update_trigger
AFTER UPDATE ON users
FOR EACH ROW
BEGIN
    UPDATE users SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS apps_update_trigger
AFTER UPDATE ON apps
FOR EACH ROW
BEGIN
    UPDATE apps SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS variables_update_trigger
AFTER UPDATE ON variables
FOR EACH ROW
BEGIN
    UPDATE variables SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS cloud_funcs_update_trigger
AFTER UPDATE ON cloud_funcs
FOR EACH ROW
BEGIN
    UPDATE cloud_funcs SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS user_apps_update_trigger
AFTER UPDATE ON user_apps
FOR EACH ROW
BEGIN
    UPDATE user_apps SET updated_at = datetime('now') WHERE user_id = NEW.user_id AND app_id = NEW.app_id;
END;

CREATE TRIGGER IF NOT EXISTS user_variables_update_trigger
AFTER UPDATE ON user_variables
FOR EACH ROW
BEGIN
    UPDATE user_variables SET updated_at = datetime('now') WHERE user_id = NEW.user_id AND variable_id = NEW.variable_id;
END;

CREATE TRIGGER IF NOT EXISTS user_cloudfuncs_update_trigger
AFTER UPDATE ON user_cloudfuncs
FOR EACH ROW
BEGIN
    UPDATE user_cloudfuncs SET updated_at = datetime('now') WHERE user_id = NEW.user_id AND cloud_func_id = NEW.cloud_func_id;
END;
