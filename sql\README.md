# 多租户认证系统数据库SQL文件

## 文件说明

本目录包含多租户认证系统的所有SQL文件，支持MySQL和SQLite3两种数据库，用于数据库的创建、初始化和部署。

## 文件结构

```
sql/
├── mysql/               # MySQL数据库文件
│   ├── master.sql      # 主数据库结构（租户信息）
│   ├── tenant.sql      # 租户数据库结构
│   ├── init_data.sql   # 初始化数据
│   └── deploy.sql      # 部署脚本
├── sqlite/              # SQLite3数据库文件
│   ├── master.sql      # 主数据库结构
│   ├── tenant.sql      # 租户数据库结构
│   ├── init_data.sql   # 初始化数据
│   ├── deploy.sh       # Linux/Mac部署脚本
│   └── deploy.bat      # Windows部署脚本
└── README.md           # 说明文档
```

## 数据库支持

### MySQL
- **版本要求**: MySQL 5.7+ (推荐8.0+)
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **特性**: 支持完整的触发器、存储过程、视图

### SQLite3
- **版本要求**: SQLite3 3.25.0+
- **字符集**: UTF-8
- **特性**: 轻量级、文件数据库、无需服务器
- **限制**: 不支持跨数据库触发器，部分统计功能需应用层实现

## 数据库架构

### 主数据库
- **MySQL**: `auth_system`
- **SQLite3**: `auth_system.db`
- **用途**: 存储租户元数据
- **表**: `tenants`

### 租户数据库
- **MySQL**: `tenant_example`, `tenant_test`, `tenant_demo`
- **SQLite3**: `tenant_example.db`, `tenant_test.db`, `tenant_demo.db`
- **用途**: 每个租户的独立数据库
- **表**: users, apps, variables, cloud_funcs, 关联表

## 部署步骤

### MySQL部署

#### 手动部署
```bash
# 1. 创建主数据库
mysql -u root -p < sql/mysql/master.sql

# 2. 创建租户数据库
mysql -u root -p tenant_example < sql/mysql/tenant.sql
mysql -u root -p tenant_test < sql/mysql/tenant.sql
mysql -u root -p tenant_demo < sql/mysql/tenant.sql

# 3. 初始化数据
mysql -u root -p < sql/mysql/init_data.sql
```

#### 使用部署脚本
```bash
mysql -u root -p < sql/mysql/deploy.sql
```

### SQLite3部署

#### Linux/Mac
```bash
# 给脚本执行权限
chmod +x sql/sqlite/deploy.sh

# 执行部署
./sql/sqlite/deploy.sh
```

#### Windows
```cmd
# 执行部署脚本
sql\sqlite\deploy.bat
```

#### 手动部署
```bash
# 创建主数据库
sqlite3 data/auth_system.db < sql/sqlite/master.sql

# 创建租户数据库
sqlite3 data/tenant_example.db < sql/sqlite/tenant.sql
sqlite3 data/tenant_test.db < sql/sqlite/tenant.sql
sqlite3 data/tenant_demo.db < sql/sqlite/tenant.sql

# 初始化数据
sqlite3 data/tenant_example.db < sql/sqlite/init_data.sql
sqlite3 data/tenant_test.db < sql/sqlite/init_data.sql
```

## SQL文件详解

### MySQL文件
- **master.sql**: 主数据库结构，包含租户表和示例数据
- **tenant.sql**: 租户数据库完整结构，包含所有表和外键约束
- **init_data.sql**: 初始化数据，包含用户、应用、变量、云函数等
- **deploy.sql**: 完整部署脚本，包含状态检查

### SQLite3文件
- **master.sql**: SQLite3兼容的主数据库结构
- **tenant.sql**: SQLite3兼容的租户数据库结构
- **init_data.sql**: SQLite3兼容的初始化数据
- **deploy.sh/deploy.bat**: 跨平台部署脚本

## 数据库配置要求

### MySQL配置
```sql
-- 创建用户和授权
CREATE USER 'auth_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON auth_system.* TO 'auth_user'@'localhost';
GRANT ALL PRIVILEGES ON tenant\_.* TO 'auth_user'@'localhost';
FLUSH PRIVILEGES;
```

### SQLite3配置
- 无需额外配置
- 数据库文件存储在 `./data/` 目录
- 自动创建配置文件 `./data/database_config.json`

## 示例数据

### 租户数据
- **示例租户**: 活跃状态，完整功能演示
- **测试租户**: 活跃状态，基础功能测试
- **演示租户**: 禁用状态，功能演示

### 用户数据
- **管理员**: <EMAIL> / password
- **普通用户**: <EMAIL>, <EMAIL> / password
- **测试用户**: <EMAIL> / password

### 应用数据
- **Web应用**: Web端应用
- **移动应用**: 移动端应用
- **API应用**: API接口应用

## GORM兼容性

### 共同特性
- 所有表包含GORM标准字段 (id, created_at, updated_at, deleted_at)
- 支持软删除功能
- 外键约束命名符合GORM规范
- 索引优化查询性能

### MySQL特有特性
- 支持完整的触发器功能
- 支持存储过程
- 支持跨数据库更新
- 支持复杂的视图和存储过程

### SQLite3特有特性
- 轻量级文件数据库
- 无需数据库服务器
- 自动时间戳触发器
- 简化的视图功能

## 注意事项

### 执行顺序
1. 必须按照 master.sql → tenant.sql → init_data.sql 的顺序执行
2. MySQL需要确保有创建数据库的权限
3. SQLite3会自动创建数据库文件

### 字符集和编码
- MySQL使用utf8mb4字符集
- SQLite3使用UTF-8编码
- 确保应用程序正确处理字符编码

### 数据安全
- 生产环境部署前请修改默认密码和密钥
- 定期备份数据库文件
- 设置适当的文件权限

### 性能考虑
- MySQL适合生产环境和高并发场景
- SQLite3适合开发、测试和小型应用
- 根据实际需求选择合适的数据库

## 故障排除

### 常见错误
1. **Access denied**: 检查MySQL用户权限
2. **File not found**: 确保SQLite3有写入权限
3. **Foreign key constraint fails**: 检查数据完整性
4. **Table already exists**: 使用IF NOT EXISTS语法

### 调试方法
```sql
-- MySQL调试
SHOW DATABASES;
SHOW TABLES;
SHOW VARIABLES LIKE 'character_set%';

-- SQLite3调试
sqlite3 database.db ".tables"
sqlite3 database.db ".schema table_name"
sqlite3 database.db "SELECT * FROM table_name LIMIT 5;"
```

## 扩展说明

### 数据迁移
- 提供了MySQL和SQLite3的双版本支持
- 可以根据需要在两种数据库间迁移数据
- 建议在开发阶段使用SQLite3，生产环境使用MySQL

### 性能优化
- 为常用查询字段添加索引
- 使用合适的字段类型和长度
- 定期维护和优化数据库

### 安全考虑
- 密码使用bcrypt加密
- 敏感信息不存储在日志中
- 支持数据脱敏查询
- 定期更新和备份数据

## 开发建议

### 开发环境
- 推荐使用SQLite3，便于开发和测试
- 使用部署脚本快速初始化开发环境
- 利用示例数据进行功能测试

### 生产环境
- 推荐使用MySQL，支持高并发和大数据量
- 配置适当的备份策略
- 监控数据库性能和资源使用

### 测试环境
- 可以使用SQLite3进行快速测试
- 或者使用MySQL的独立实例
- 确保测试数据与生产环境隔离
