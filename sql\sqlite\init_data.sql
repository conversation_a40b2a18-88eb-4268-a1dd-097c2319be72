-- SQLite3 租户数据库初始化数据示例
-- 注意：此文件需要在创建租户数据库后执行

-- 启用外键约束
PRAGMA foreign_keys = ON;

-- 为示例租户插入用户数据 (tenant_id = 1)
INSERT OR IGNORE INTO users (username, email, password, first_name, last_name, phone, status, tenant_id) VALUES
('admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统', '管理员', '13800138000', 'active', 1),
('user1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '张', '三', '13800138001', 'active', 1),
('user2', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '李', '四', '13800138002', 'active', 1);

-- 插入应用数据
INSERT OR IGNORE INTO apps (name, description, app_key, app_secret, status, tenant_id) VALUES
('Web应用', 'Web端应用', 'app_key_web_001', 'app_secret_web_001', 'active', 1),
('移动应用', '移动端应用', 'app_key_mobile_001', 'app_secret_mobile_001', 'active', 1),
('API应用', 'API接口应用', 'app_key_api_001', 'app_secret_api_001', 'active', 1);

-- 插入变量数据
INSERT OR IGNORE INTO variables (name, value, description, type, status, app_id, tenant_id) VALUES
('api_url', 'https://api.example.com', 'API接口地址', 'string', 'active', 1, 1),
('timeout', '30', '请求超时时间(秒)', 'string', 'active', 1, 1),
('enable_debug', 'true', '是否启用调试模式', 'string', 'active', 1, 1),
('feature_flags', '{"new_ui": true, "beta_features": false}', '功能开关配置', 'string', 'active', 1, 1),
('max_retries', '3', '最大重试次数', 'string', 'active', 2, 1),
('theme', 'dark', '应用主题', 'string', 'active', 2, 1),
('push_enabled', 'true', '是否启用推送', 'string', 'active', 2, 1),
('rate_limit', '1000', 'API调用限制', 'string', 'active', 3, 1),
('cache_ttl', '3600', '缓存过期时间(秒)', 'string', 'active', 3, 1);

-- 插入云函数数据
INSERT OR IGNORE INTO cloud_funcs (name, description, code, runtime, status, app_id, tenant_id) VALUES
('send_email', '发送邮件函数', 'package main

import (
    "fmt"
    "net/smtp"
)

func SendEmail(to, subject, body string) error {
    // 邮件发送逻辑
    return nil
}', 'go1.21', 'active', 1, 1),
('process_data', '数据处理函数', 'function processData(data) {
    // 数据处理逻辑
    return data;
}', 'nodejs18', 'active', 1, 1),
('generate_report', '生成报表函数', 'def generate_report(data):
    # 报表生成逻辑
    return report', 'python3.9', 'active', 2, 1),
('validate_input', '输入验证函数', 'package main

import "regexp"

func ValidateEmail(email string) bool {
    pattern := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
    match, _ := regexp.MatchString(pattern, email)
    return match
}', 'go1.21', 'active', 3, 1);

-- 插入用户应用关联数据
INSERT OR IGNORE INTO user_apps (user_id, app_id) VALUES
(1, 1), (1, 2), (1, 3), -- 管理员拥有所有应用权限
(2, 1), (2, 2),         -- 用户1拥有Web和移动应用权限
(3, 1);                  -- 用户2只拥有Web应用权限

-- 插入用户变量关联数据
INSERT OR IGNORE INTO user_variables (user_id, variable_id) VALUES
(1, 1), (1, 2), (1, 3), (1, 4), -- 管理员关联所有变量
(2, 1), (2, 2),                 -- 用户1关联部分变量
(3, 1);                         -- 用户2关联少量变量

-- 插入用户云函数关联数据
INSERT OR IGNORE INTO user_cloudfuncs (user_id, cloud_func_id) VALUES
(1, 1), (1, 2), (1, 3), (1, 4), -- 管理员关联所有云函数
(2, 1), (2, 2),                 -- 用户1关联部分云函数
(3, 1);                         -- 用户2关联少量云函数

-- 为测试租户插入基础数据 (tenant_id = 2)
INSERT OR IGNORE INTO users (username, email, password, first_name, last_name, status, tenant_id) VALUES
('test_admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Test', 'Admin', 'active', 2);

INSERT OR IGNORE INTO apps (name, description, app_key, app_secret, status, tenant_id) VALUES
('Test App', '测试应用', 'test_app_key_001', 'test_app_secret_001', 'active', 2);

-- 创建视图：租户统计信息 (在主数据库中)
-- 注意：这个视图需要在主数据库中创建，这里只是示例
CREATE VIEW IF NOT EXISTS tenant_stats AS
SELECT 
    t.id,
    t.name,
    t.database_name,
    t.status,
    t.max_apps,
    t.max_users,
    t.max_variables,
    t.max_cloud_funcs,
    t.current_apps,
    t.current_users,
    t.current_variables,
    t.current_cloud_funcs,
    CASE 
        WHEN t.status = 'active' THEN '运行中'
        WHEN t.status = 'disabled' THEN '已禁用'
        WHEN t.status = 'suspended' THEN '已暂停'
        ELSE '未知'
    END as status_text,
    CAST(ROUND(CAST(t.current_apps AS REAL) / t.max_apps * 100, 1) AS TEXT) || '%' as apps_usage,
    CAST(ROUND(CAST(t.current_users AS REAL) / t.max_users * 100, 1) AS TEXT) || '%' as users_usage,
    CAST(ROUND(CAST(t.current_variables AS REAL) / t.max_variables * 100, 1) AS TEXT) || '%' as variables_usage,
    CAST(ROUND(CAST(t.current_cloud_funcs AS REAL) / t.max_cloud_funcs * 100, 1) AS TEXT) || '%' as cloudfuncs_usage,
    t.created_at,
    t.updated_at
FROM tenants t;

-- 创建触发器：更新租户统计信息
-- 注意：SQLite3的触发器语法与MySQL不同，这里提供示例

-- 更新用户数量统计
CREATE TRIGGER IF NOT EXISTS update_tenant_stats_after_user_insert
AFTER INSERT ON users
FOR EACH ROW
BEGIN
    -- 这里需要通过应用层逻辑来更新主数据库中的统计信息
    -- SQLite3无法直接跨数据库更新
END;

-- 更新应用数量统计
CREATE TRIGGER IF NOT EXISTS update_tenant_stats_after_app_insert
AFTER INSERT ON apps
FOR EACH ROW
BEGIN
    -- 这里需要通过应用层逻辑来更新主数据库中的统计信息
    -- SQLite3无法直接跨数据库更新
END;

-- 创建一些有用的查询视图
CREATE VIEW IF NOT EXISTS user_apps_view AS
SELECT 
    u.id as user_id,
    u.username,
    u.email,
    u.status as user_status,
    a.id as app_id,
    a.name as app_name,
    a.app_key,
    a.status as app_status
FROM users u
JOIN user_apps ua ON u.id = ua.user_id
JOIN apps a ON ua.app_id = a.id
WHERE u.deleted_at IS NULL AND a.deleted_at IS NULL;

CREATE VIEW IF NOT EXISTS app_variables_view AS
SELECT 
    a.id as app_id,
    a.name as app_name,
    v.id as variable_id,
    v.name as variable_name,
    v.value,
    v.type,
    v.description,
    v.status as variable_status
FROM apps a
JOIN variables v ON a.id = v.app_id
WHERE a.deleted_at IS NULL AND v.deleted_at IS NULL;

CREATE VIEW IF NOT EXISTS active_users_view AS
SELECT 
    u.id,
    u.username,
    u.email,
    u.first_name,
    u.last_name,
    u.phone,
    u.last_login_at,
    COUNT(DISTINCT ua.app_id) as app_count,
    COUNT(DISTINCT uv.variable_id) as variable_count,
    COUNT(DISTINCT uc.cloud_func_id) as cloudfunc_count
FROM users u
LEFT JOIN user_apps ua ON u.id = ua.user_id
LEFT JOIN user_variables uv ON u.id = uv.user_id
LEFT JOIN user_cloudfuncs uc ON u.id = uc.user_id
WHERE u.status = 'active' AND u.deleted_at IS NULL
GROUP BY u.id, u.username, u.email, u.first_name, u.last_name, u.phone, u.last_login_at;
