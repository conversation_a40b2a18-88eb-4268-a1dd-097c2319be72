# 多租户认证系统API文档

基于go-zero的多租户认证系统API定义，采用模块化设计，支持MySQL和SQLite3数据库。

## API结构

```
api/
├── auth.api              # 主API文件，整合所有模块
├── types/
│   └── types.api         # 公共类型定义
├── tenant/
│   └── tenant.api        # 租户管理API
├── user/
│   └── user.api          # 用户管理API
├── app/
│   └── app.api           # 应用管理API
├── variable/
│   └── variable.api      # 变量管理API
├── cloudfunc/
│   └── cloudfunc.api     # 云函数管理API
├── auth/
│   └── auth.api          # 认证管理API
└── README.md             # API文档
```

## 模块说明

### 1. 公共类型 (types/types.api)
- **AmisResponse**: 基础响应结构
- **PageRequest/PageResponse**: 分页请求和响应
- **TenantInfo**: 租户信息结构
- **UserInfo**: 用户信息结构（安全）
- **AppInfo**: 应用信息结构（安全）
- **VariableInfo**: 变量信息结构
- **CloudFuncInfo**: 云函数信息结构（安全）
- **AuthRequest/Response**: 认证相关结构
- **枚举定义**: 状态、类型、运行时等

### 2. 租户管理 (tenant/tenant.api)
**功能**: 租户的创建、查询、更新、删除等管理功能

**主要接口**:
- `POST /api/v1/tenants` - 创建租户
- `GET /api/v1/tenants` - 获取租户列表
- `GET /api/v1/tenants/:id` - 获取租户详情
- `PUT /api/v1/tenants/:id` - 更新租户
- `DELETE /api/v1/tenants/:id` - 删除租户
- `POST /api/v1/tenants/:id/enable` - 启用租户
- `POST /api/v1/tenants/:id/disable` - 禁用租户
- `GET /api/v1/tenants/:id/stats` - 获取统计信息

### 3. 用户管理 (user/user.api)
**功能**: 用户的创建、查询、更新、删除、认证等管理功能

**主要接口**:
- `POST /api/v1/tenants/:tenantId/users` - 创建用户
- `GET /api/v1/tenants/:tenantId/users` - 获取用户列表
- `GET /api/v1/tenants/:tenantId/users/:id` - 获取用户详情
- `PUT /api/v1/tenants/:tenantId/users/:id` - 更新用户
- `DELETE /api/v1/tenants/:tenantId/users/:id` - 删除用户
- `POST /api/v1/tenants/:tenantId/users/login` - 用户登录
- `POST /api/v1/tenants/:tenantId/users/logout` - 用户登出
- `PUT /api/v1/tenants/:tenantId/users/:id/password` - 修改密码

### 4. 应用管理 (app/app.api)
**功能**: 应用的创建、查询、更新、删除、密钥管理等

**主要接口**:
- `POST /api/v1/tenants/:tenantId/apps` - 创建应用
- `GET /api/v1/tenants/:tenantId/apps` - 获取应用列表
- `GET /api/v1/tenants/:tenantId/apps/:id` - 获取应用详情
- `PUT /api/v1/tenants/:tenantId/apps/:id` - 更新应用
- `DELETE /api/v1/tenants/:tenantId/apps/:id` - 删除应用
- `GET /api/v1/tenants/:tenantId/apps/:id/key` - 获取应用密钥
- `POST /api/v1/tenants/:tenantId/apps/:id/key/regenerate` - 重新生成密钥

### 5. 变量管理 (variable/variable.api)
**功能**: 变量的创建、查询、更新、删除、值管理等

**主要接口**:
- `POST /api/v1/tenants/:tenantId/apps/:appId/variables` - 创建变量
- `GET /api/v1/tenants/:tenantId/apps/:appId/variables` - 获取变量列表
- `GET /api/v1/tenants/:tenantId/apps/:appId/variables/:id` - 获取变量详情
- `PUT /api/v1/tenants/:tenantId/apps/:appId/variables/:id` - 更新变量
- `DELETE /api/v1/tenants/:tenantId/apps/:appId/variables/:id` - 删除变量
- `PUT /api/v1/tenants/:tenantId/apps/:appId/variables/:id/value` - 更新变量值

### 6. 云函数管理 (cloudfunc/cloudfunc.api)
**功能**: 云函数的创建、查询、更新、删除、执行等

**主要接口**:
- `POST /api/v1/tenants/:tenantId/apps/:appId/cloudfuncs` - 创建云函数
- `GET /api/v1/tenants/:tenantId/apps/:appId/cloudfuncs` - 获取云函数列表
- `GET /api/v1/tenants/:tenantId/apps/:appId/cloudfuncs/:id` - 获取云函数详情
- `PUT /api/v1/tenants/:tenantId/apps/:appId/cloudfuncs/:id` - 更新云函数
- `DELETE /api/v1/tenants/:tenantId/apps/:appId/cloudfuncs/:id` - 删除云函数
- `POST /api/v1/tenants/:tenantId/apps/:appId/cloudfuncs/:id/execute` - 执行云函数

### 7. 认证管理 (auth/auth.api)
**功能**: 应用认证、用户认证、权限验证、网络验证等

**主要接口**:
- `POST /api/v1/auth/app` - 应用认证
- `POST /api/v1/auth/user` - 用户认证
- `POST /api/v1/auth/token/validate` - Token验证
- `POST /api/v1/auth/permission/check` - 权限检查
- `POST /api/v1/auth/token/refresh` - 刷新Token
- `POST /api/v1/auth/logout` - 用户登出
- `POST /api/v1/auth/network` - 网络验证

### 8. 系统管理 (system - 在主API文件中)
**功能**: 系统信息、健康检查、配置管理等

**主要接口**:
- `GET /api/v1/system/info` - 系统信息
- `GET /api/v1/system/health` - 健康检查
- `GET /api/v1/system/config` - 系统配置
- `PUT /api/v1/system/config` - 更新配置
- `GET /api/v1/system/stats` - 系统统计

## 数据模型

### 租户 (Tenant)
- 支持资源限制（应用、用户、变量、云函数数量）
- 实时使用统计
- 状态管理（启用/禁用/暂停）
- 独立数据库支持

### 用户 (User)
- 基本信息管理
- 密码加密存储
- 权限关联（应用、变量、云函数）
- 登录状态管理

### 应用 (App)
- 密钥认证机制
- 状态管理
- 关联资源管理
- 访问统计

### 变量 (Variable)
- 多种数据类型支持（string、number、boolean、json）
- 值验证
- 状态管理
- 用户权限关联

### 云函数 (CloudFunc)
- 多运行时支持（Go、Node.js、Python）
- 代码管理
- 执行功能
- 版本控制

## 认证机制

### 应用认证
- 基于AppKey和AppSecret
- 返回访问令牌
- 支持令牌刷新

### 用户认证
- 用户名/密码认证
- JWT令牌管理
- 会话管理
- 权限验证

### 网络验证
- 应用级别验证
- 资源级别权限检查
- 支持批量权限验证

## 权限系统

### 资源类型
- **app**: 应用资源
- **variable**: 变量资源
- **cloudfunc**: 云函数资源

### 操作类型
- **read**: 读取权限
- **write**: 写入权限
- **execute**: 执行权限

### 权限检查
- 单资源权限检查
- 批量权限检查
- 用户权限查询
- 应用权限查询

## 分页支持

所有列表接口都支持分页：
- `page`: 页码（从1开始）
- `pageSize`: 每页数量（1-100）

## 过滤支持

主要过滤条件：
- **状态过滤**: active、inactive、disabled、suspended
- **名称过滤**: 支持模糊查询
- **类型过滤**: 变量类型、运行时等
- **时间过滤**: 创建时间、更新时间

## 验证规则

### 通用验证
- 必填字段检查
- 字段长度限制
- 格式验证（邮箱、URL等）
- 枚举值验证

### 业务验证
- 唯一性检查（用户名、邮箱、应用名等）
- 权限验证
- 资源限制检查
- 状态验证

## 响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体数据
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "error message",
  "data": null
}
```

### 分页响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [],
    "total": 100,
    "page": 1,
    "pageSize": 10
  }
}
```

## 安全特性

### 数据安全
- 敏感信息过滤（密码、密钥等）
- 数据脱敏
- 访问令牌管理

### 访问控制
- 基于租户的数据隔离
- 权限验证
- 速率限制

### 审计日志
- 操作日志记录
- 认证日志
- 异常访问记录

## 使用示例

### 创建租户
```bash
curl -X POST http://localhost:8080/api/v1/tenants \
  -H "Content-Type: application/json" \
  -d '{
    "name": "示例租户",
    "databaseName": "tenant_example",
    "maxApps": 10,
    "maxUsers": 100,
    "maxVariables": 1000,
    "maxCloudFuncs": 50
  }'
```

### 用户登录
```bash
curl -X POST http://localhost:8080/api/v1/tenants/1/users/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "password"
  }'
```

### 应用认证
```bash
curl -X POST http://localhost:8080/api/v1/auth/app \
  -H "Content-Type: application/json" \
  -d '{
    "appKey": "app_key_web_001",
    "appSecret": "app_secret_web_001",
    "tenantId": 1
  }'
```

### 获取应用列表
```bash
curl -X GET "http://localhost:8080/api/v1/tenants/1/apps?page=1&pageSize=10" \
  -H "Authorization: Bearer your-token"
```

## 开发指南

### 生成代码
```bash
# 生成所有API代码
goctl api go -api api/auth.api -dir .

# 生成指定模块代码
goctl api go -api api/tenant/tenant.api -dir ./tenant
```

### 运行服务
```bash
# 启动服务
go run main.go

# 或使用go-zero
go run auth.go -f etc/auth.yaml
```

### 测试API
```bash
# 使用curl测试
curl -X GET http://localhost:8080/api/v1/system/health

# 使用Postman
# 导入API文档进行测试
```

## 扩展指南

### 添加新模块
1. 在api目录创建新模块文件夹
2. 创建.api文件定义接口
3. 在主API文件中导入新模块
4. 生成对应的handler代码

### 添加新接口
1. 在对应模块的.api文件中添加接口定义
2. 实现对应的handler
3. 更新权限验证逻辑

### 修改数据模型
1. 更新数据库SQL文件
2. 更新API类型定义
3. 更新handler逻辑
4. 更新验证规则

## 注意事项

1. **模块化设计**: 每个模块独立，避免循环依赖
2. **权限验证**: 所有接口都需要适当的权限检查
3. **数据验证**: 严格验证输入数据
4. **错误处理**: 统一的错误响应格式
5. **日志记录**: 记录重要操作和异常
6. **性能考虑**: 避免N+1查询，使用缓存优化

## 版本历史

- **v1.0.0**: 初始版本，支持完整的租户管理功能
- **v1.1.0**: 添加云函数管理功能
- **v1.2.0**: 添加网络验证功能
- **v1.3.0**: 支持SQLite3数据库
