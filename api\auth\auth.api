syntax = "v1"

info (
    title:   "认证API"
    desc:    "应用认证、用户认证、权限验证等功能"
    author:  "auth-system"
    email:   "<EMAIL>"
    version: "v1.0.0"
)

import (
    "types/types.api"
    "types/amis.api"
)

// 应用认证请求
type AppAuthRequest {
    AppKey    string `json:"appKey"    validate:"required"`
    AppSecret string `json:"appSecret" validate:"required"`
    TenantId  uint   `json:"tenantId"  validate:"required"`
}

// 应用认证响应
type AppAuthResponse {
    Valid   bool     `json:"valid"`
    AppInfo *AppInfo `json:"appInfo,omitempty"`
    Token   string   `json:"token,omitempty"`
}

// 用户认证请求
type UserAuthRequest {
    Username string `json:"username" validate:"required"`
    Password string `json:"password" validate:"required,min=6,max=255"`
    TenantId uint   `json:"tenantId" validate:"required"`
}

// 用户认证响应
type UserAuthResponse {
    Valid     bool     `json:"valid"`
    UserInfo  *UserInfo `json:"userInfo,omitempty"`
    Token     string   `json:"token,omitempty"`
    ExpiresAt string   `json:"expiresAt,omitempty"`
}

// Token验证请求
type TokenValidateRequest {
    Token string `json:"token" validate:"required"`
}

// Token验证响应
type TokenValidateResponse {
    Valid    bool     `json:"valid"`
    UserInfo *UserInfo `json:"userInfo,omitempty"`
    AppInfo  *AppInfo  `json:"appInfo,omitempty"`
}

// 权限验证请求
type PermissionCheckRequest {
    Resource string `json:"resource" validate:"required"` // 资源类型：app, variable, cloudfunc
    Action   string `json:"action"   validate:"required"` // 操作类型：read, write, execute
    ResourceId uint `json:"resourceId" validate:"required"` // 资源ID
}

// 权限验证响应
type PermissionCheckResponse {
    Allowed bool   `json:"allowed"`
    Message string `json:"message"`
}

// 刷新Token请求
type RefreshTokenRequest {
    RefreshToken string `json:"refreshToken" validate:"required"`
}

// 刷新Token响应
type RefreshTokenResponse {
    Token     string `json:"token"`
    ExpiresAt string `json:"expiresAt"`
}

// 登出请求
type LogoutRequest {
    Token string `json:"token" validate:"required"`
}

// 网络验证请求
type NetworkAuthRequest {
    AppKey    string `json:"appKey"    validate:"required"`
    AppSecret string `json:"appSecret" validate:"required"`
    TenantId  uint   `json:"tenantId"  validate:"required"`
    Resource  string `json:"resource"  validate:"required"` // 请求的资源类型
    Action    string `json:"action"    validate:"required"` // 请求的操作类型
}

// 网络验证响应
type NetworkAuthResponse {
    Authorized bool                   `json:"authorized"`
    UserInfo   *UserInfo              `json:"userInfo,omitempty"`
    AppInfo    *AppInfo               `json:"appInfo,omitempty"`
    Permissions map[string]interface{} `json:"permissions,omitempty"`
}

// 应用列表请求
type AppListRequest {
    TenantId uint `json:"tenantId" validate:"required"`
    PageRequest
}

// 用户列表请求
type UserListRequest {
    TenantId uint `json:"tenantId" validate:"required"`
    PageRequest
}

// 变量列表请求
type VariableListRequest {
    TenantId uint `json:"tenantId" validate:"required"`
    AppId    uint `json:"appId"    validate:"required"`
    PageRequest
}

// 云函数列表请求
type CloudFuncListRequest {
    TenantId uint `json:"tenantId" validate:"required"`
    AppId    uint `json:"appId"    validate:"required"`
    PageRequest
}

// 认证统计信息
type AuthStats {
    TotalAuths     int64 `json:"totalAuths"`     // 总认证次数
    SuccessfulAuths int64 `json:"successfulAuths"` // 成功认证次数
    FailedAuths    int64 `json:"failedAuths"`    // 失败认证次数
    ActiveTokens   int64 `json:"activeTokens"`   // 活跃Token数量
    TodayAuths     int64 `json:"todayAuths"`     // 今日认证次数
}

@server (
    group: auth
    prefix: /api/v1
)
service auth {
    @doc (
        summary: "应用认证"
        description: "验证应用密钥，返回应用信息和访问令牌"
    )
    @handler AppAuthHandler
    post /auth/app (AppAuthRequest) returns (AmisResponse)

    @doc (
        summary: "用户认证"
        description: "验证用户凭据，返回用户信息和访问令牌"
    )
    @handler UserAuthHandler
    post /auth/user (UserAuthRequest) returns (AmisResponse)

    @doc (
        summary: "Token验证"
        description: "验证访问令牌的有效性"
    )
    @handler TokenValidateHandler
    post /auth/token/validate (TokenValidateRequest) returns (AmisResponse)

    @doc (
        summary: "权限检查"
        description: "检查用户是否有指定资源的操作权限"
    )
    @handler PermissionCheckHandler
    post /auth/permission/check (PermissionCheckRequest) returns (AmisResponse)

    @doc (
        summary: "刷新Token"
        description: "使用刷新令牌获取新的访问令牌"
    )
    @handler RefreshTokenHandler
    post /auth/token/refresh (RefreshTokenRequest) returns (AmisResponse)

    @doc (
        summary: "用户登出"
        description: "使用户登出，失效访问令牌"
    )
    @handler LogoutHandler
    post /auth/logout (LogoutRequest) returns (AmisResponse)

    @doc (
        summary: "网络验证"
        description: "网络层面的应用认证和权限验证"
    )
    @handler NetworkAuthHandler
    post /auth/network (NetworkAuthRequest) returns (AmisResponse)

    @doc (
        summary: "获取应用列表"
        description: "获取租户下的应用列表（网络验证）"
    )
    @handler GetAppListHandler
    get /auth/tenants/:tenantId/apps (AppListRequest) returns (AmisResponse)

    @doc (
        summary: "获取用户列表"
        description: "获取租户下的用户列表（网络验证）"
    )
    @handler GetUserListHandler
    get /auth/tenants/:tenantId/users (UserListRequest) returns (AmisResponse)

    @doc (
        summary: "获取变量列表"
        description: "获取应用下的变量列表（网络验证）"
    )
    @handler GetVariableListHandler
    get /auth/tenants/:tenantId/apps/:appId/variables (VariableListRequest) returns (AmisResponse)

    @doc (
        summary: "获取云函数列表"
        description: "获取应用下的云函数列表（网络验证）"
    )
    @handler GetCloudFuncListHandler
    get /auth/tenants/:tenantId/apps/:appId/cloudfuncs (CloudFuncListRequest) returns (AmisResponse)

    @doc (
        summary: "获取认证统计信息"
        description: "获取系统的认证统计信息"
    )
    @handler GetAuthStatsHandler
    get /auth/stats (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "获取用户权限信息"
        description: "获取用户的所有权限信息"
    )
    @handler GetUserPermissionsHandler
    get /auth/users/:userId/permissions (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "获取应用权限信息"
        description: "获取应用的所有权限信息"
    )
    @handler GetAppPermissionsHandler
    get /auth/apps/:appId/permissions (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "批量权限检查"
        description: "批量检查多个资源的权限"
    )
    @handler BatchPermissionCheckHandler
    post /auth/permission/batch-check ([]PermissionCheckRequest) returns (AmisResponse)

    @doc (
        summary: "获取活跃会话"
        description: "获取用户的活跃会话列表"
    )
    @handler GetActiveSessionsHandler
    get /auth/users/:userId/sessions (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "终止会话"
        description: "终止指定的用户会话"
    )
    @handler TerminateSessionHandler
    delete /auth/users/:userId/sessions/:sessionId (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "获取认证日志"
        description: "获取认证相关的日志记录"
    )
    @handler GetAuthLogsHandler
    get /auth/logs (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "健康检查"
        description: "认证服务的健康检查"
    )
    @handler HealthCheckHandler
    get /auth/health (AmisResponse) returns (AmisResponse)
}
