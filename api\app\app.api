syntax = "v1"

info (
    title:   "应用管理API"
    desc:    "应用的创建、查询、更新、删除等管理功能"
    author:  "auth-system"
    email:   "<EMAIL>"
    version: "v1.0.0"
)

import (
    "types/types.api"
    "types/amis.api"
)
// 应用凭证信息
type AppCredential {
    AppKey    string `json:"appKey"`
    AppSecret string `json:"appSecret"`
}

// 创建应用请求
type CreateAppRequest {
    Name        string `json:"name"        validate:"required,min=1,max=100"`
    Description string `json:"description" validate:"max=1000"`
    Status      string `json:"status"      validate:"oneof=active inactive suspended"`
}

// 更新应用请求
type UpdateAppRequest {
    Name        string `json:"name"        validate:"required,min=1,max=100"`
    Description string `json:"description" validate:"max=1000"`
    Status      string `json:"status"      validate:"oneof=active inactive suspended"`
}

// 更新应用状态请求
type UpdateAppStatusRequest {
    Status string `json:"status" validate:"required,oneof=active inactive suspended"`
}

// 应用列表查询请求
type AppListRequest {
    PageRequest
    Name        string `json:"name"        validate:"max=100"`   // 应用名称过滤
    Status      string `json:"status"      validate:"oneof=active inactive suspended"` // 状态过滤
}

// 应用密钥请求
type AppKeyRequest {
    AppId uint `json:"appId" validate:"required"`
}

// 应用密钥响应
type AppKeyResponse {
    AppCredential
    AppId uint `json:"appId"`
}

// 应用详情响应
type AppDetailResponse {
    AppInfo
    Variables   []VariableInfo  `json:"variables"`   // 应用包含的变量
    CloudFuncs  []CloudFuncInfo `json:"cloudFuncs"`  // 应用包含的云函数
    UserCount   int             `json:"userCount"`   // 关联用户数量
}

// 应用统计信息
type AppStats {
    Id          uint   `json:"id"`
    Name        string `json:"name"`
    Status      string `json:"status"`
    UserCount   int    `json:"userCount"`
    VariableCount int  `json:"variableCount"`
    CloudFuncCount int `json:"cloudFuncCount"`
    CreatedAt   string `json:"createdAt"`
    UpdatedAt   string `json:"updatedAt"`
}

@server (
    group: app
    prefix: /api/v1
)
service app {
    @doc (
        summary: "创建应用"
        description: "在指定租户下创建新应用，自动生成密钥"
    )
    @handler CreateAppHandler
    post /tenants/:tenantId/apps (CreateAppRequest) returns (AmisResponse)

    @doc (
        summary: "获取应用详情"
        description: "根据应用ID获取应用详细信息"
    )
    @handler GetAppHandler
    get /tenants/:tenantId/apps/:id (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "更新应用信息"
        description: "更新应用的基本信息"
    )
    @handler UpdateAppHandler
    put /tenants/:tenantId/apps/:id (UpdateAppRequest) returns (AmisResponse)

    @doc (
        summary: "删除应用"
        description: "删除指定应用及其相关数据"
    )
    @handler DeleteAppHandler
    delete /tenants/:tenantId/apps/:id (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "获取应用列表"
        description: "分页获取应用列表，支持名称和状态过滤"
    )
    @handler ListAppsHandler
    get /tenants/:tenantId/apps (AppListRequest) returns (AmisResponse)

    @doc (
        summary: "更新应用状态"
        description: "更新应用状态（启用/禁用/暂停）"
    )
    @handler UpdateAppStatusHandler
    put /tenants/:tenantId/apps/:id/status (UpdateAppStatusRequest) returns (AmisResponse)

    @doc (
        summary: "获取应用密钥"
        description: "获取应用的密钥信息"
    )
    @handler GetAppKeyHandler
    get /tenants/:tenantId/apps/:id/key (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "重新生成应用密钥"
        description: "重新生成应用的密钥信息"
    )
    @handler RegenerateAppKeyHandler
    post /tenants/:tenantId/apps/:id/key/regenerate (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "获取应用统计信息"
        description: "获取应用的统计信息"
    )
    @handler GetAppStatsHandler
    get /tenants/:tenantId/apps/:id/stats (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "获取所有应用统计概览"
        description: "获取租户下所有应用的统计概览"
    )
    @handler GetAllAppsStatsHandler
    get /tenants/:tenantId/apps/stats/overview (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "检查应用名称可用性"
        description: "检查应用名称是否已被使用"
    )
    @handler CheckAppNameHandler
    get /tenants/:tenantId/apps/check-name/:name (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "获取应用用户列表"
        description: "获取应用关联的用户列表"
    )
    @handler GetAppUsersHandler
    get /tenants/:tenantId/apps/:id/users (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "批量更新应用状态"
        description: "批量更新多个应用的状态"
    )
    @handler BatchUpdateAppStatusHandler
    put /tenants/:tenantId/apps/batch-status (UpdateAppStatusRequest) returns (AmisResponse)

    @doc (
        summary: "获取应用活跃状态"
        description: "检查应用是否处于活跃状态"
    )
    @handler GetAppActiveStatusHandler
    get /tenants/:tenantId/apps/:id/active-status (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "获取应用资源使用情况"
        description: "获取应用的资源使用情况"
    )
    @handler GetAppResourceUsageHandler
    get /tenants/:tenantId/apps/:id/resource-usage (AmisResponse) returns (AmisResponse)
}
