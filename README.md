# 多租户认证系统

基于Go语言和GORM的多租户认证系统，支持每个租户独立数据库，提供完整的租户管理和网络验证功能。

## 功能特性

### 多租户数据库设计
- 每个租户拥有独立的数据库
- 管理员可以设置租户的应用、用户、变量、云函数数量限制
- 支持启用/禁用租户
- 自动创建和管理租户数据库

### 网络验证
- 应用列表查询
- 用户列表查询
- 关联应用的变量列表查询
- 关联应用的云函数列表查询
- 应用密钥验证

## 项目结构

```
auth-system_tanat/
├── config/              # 配置文件
│   └── database.go      # 数据库配置
├── handlers/            # HTTP处理器
│   └── auth_handler.go  # 认证处理器
├── models/              # 数据模型
│   ├── tenant.go        # 租户模型
│   ├── user.go          # 用户模型
│   ├── app.go           # 应用模型
│   ├── variable.go      # 变量模型
│   └── cloudfunc.go     # 云函数模型
├── services/            # 业务逻辑
│   └── tenant_service.go # 租户服务
├── main.go              # 主程序
├── go.mod               # Go模块配置
├── .env.example         # 环境变量示例
└── README.md            # 项目文档
```

## 数据模型

### Tenant (租户)
- 基本信息：名称、数据库名称、状态
- 资源限制：最大应用数、用户数、变量数、云函数数
- 使用统计：当前资源使用情况
- 配置信息：JSON格式的自定义配置

### User (用户)
- 基本信息：用户名、邮箱、密码、姓名、电话
- 状态管理：活跃、非活跃、暂停
- 关联关系：属于租户，可关联多个应用、变量、云函数

### App (应用)
- 基本信息：名称、描述、应用密钥、应用密钥
- 状态管理：活跃、非活跃、暂停
- 关联关系：属于租户，包含多个变量、云函数

### Variable (变量)
- 基本信息：名称、值、描述、类型
- 状态管理：活跃、非活跃
- 关联关系：属于应用和租户，可关联多个用户

### CloudFunc (云函数)
- 基本信息：名称、描述、代码、运行时
- 状态管理：活跃、非活跃、部署中
- 关联关系：属于应用和租户，可关联多个用户

## API接口

### 租户管理
- `POST /api/v1/tenants` - 创建租户
- `GET /api/v1/tenants` - 获取租户列表
- `GET /api/v1/tenants/{id}` - 获取租户详情
- `PUT /api/v1/tenants/{id}` - 更新租户
- `DELETE /api/v1/tenants/{id}` - 删除租户
- `POST /api/v1/tenants/{id}/enable` - 启用租户
- `POST /api/v1/tenants/{id}/disable` - 禁用租户

### 网络验证
- `GET /api/v1/auth/tenants/{tenantId}/apps` - 获取应用列表
- `GET /api/v1/auth/tenants/{tenantId}/users` - 获取用户列表
- `GET /api/v1/auth/tenants/{tenantId}/apps/{appId}/variables` - 获取应用变量列表
- `GET /api/v1/auth/tenants/{tenantId}/apps/{appId}/cloudfuncs` - 获取应用云函数列表
- `POST /api/v1/auth/tenants/{tenantId}/validate` - 验证应用

## 安装和运行

### 环境要求
- Go 1.21+
- MySQL 5.7+

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd auth-system_tanat
```

2. 安装依赖
```bash
go mod tidy
```

3. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接信息
```

4. 创建数据库
```sql
CREATE DATABASE auth_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

5. 运行项目
```bash
go run main.go
```

### 使用示例

#### 创建租户
```bash
curl -X POST http://localhost:8080/api/v1/tenants \
  -H "Content-Type: application/json" \
  -d '{
    "name": "示例租户",
    "database_name": "tenant_example",
    "max_apps": 10,
    "max_users": 100,
    "max_variables": 1000,
    "max_cloud_funcs": 50
  }'
```

#### 获取应用列表
```bash
curl -X GET http://localhost:8080/api/v1/auth/tenants/1/apps
```

#### 验证应用
```bash
curl -X POST http://localhost:8080/api/v1/auth/tenants/1/validate \
  -H "X-App-Key: your-app-key" \
  -H "X-App-Secret: your-app-secret"
```

## 安全特性

- 数据隔离：每个租户使用独立数据库
- 认证机制：应用密钥验证
- 权限控制：基于租户的访问控制
- 敏感信息过滤：返回数据中不包含密码等敏感信息

## 扩展功能

### 可添加的功能
- JWT认证
- 用户角色管理
- 操作日志记录
- 数据备份和恢复
- 性能监控
- 缓存机制

### 性能优化
- 数据库连接池
- 分页查询优化
- 索引优化
- 读写分离

## 许可证

MIT License
