-- 租户数据库结构 - 每个租户独立数据库
-- 数据库: tenant_* (动态数据库名)

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `created_at` datetime(3) DEFAULT NULL,
    `updated_at` datetime(3) DEFAULT NULL,
    `deleted_at` datetime(3) DEFAULT NULL,
    `username` varchar(50) NOT NULL,
    `email` varchar(100) NOT NULL,
    `password` varchar(255) NOT NULL,
    `first_name` varchar(50) DEFAULT NULL,
    `last_name` varchar(50) DEFAULT NULL,
    `phone` varchar(20) DEFAULT NULL,
    `status` varchar(20) NOT NULL DEFAULT 'active',
    `last_login_at` datetime(3) DEFAULT NULL,
    `tenant_id` bigint unsigned NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_users_username` (`username`),
    UNIQUE KEY `idx_users_email` (`email`),
    KEY `idx_users_status` (`status`),
    KEY `idx_users_tenant_id` (`tenant_id`),
    KEY `idx_users_deleted_at` (`deleted_at`),
    KEY `idx_users_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 应用表
CREATE TABLE IF NOT EXISTS `apps` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `created_at` datetime(3) DEFAULT NULL,
    `updated_at` datetime(3) DEFAULT NULL,
    `deleted_at` datetime(3) DEFAULT NULL,
    `name` varchar(100) NOT NULL,
    `description` text,
    `app_key` varchar(255) NOT NULL,
    `app_secret` varchar(255) NOT NULL,
    `status` varchar(20) NOT NULL DEFAULT 'active',
    `tenant_id` bigint unsigned NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_apps_app_key` (`app_key`),
    KEY `idx_apps_status` (`status`),
    KEY `idx_apps_tenant_id` (`tenant_id`),
    KEY `idx_apps_deleted_at` (`deleted_at`),
    KEY `idx_apps_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应用表';

-- 变量表
CREATE TABLE IF NOT EXISTS `variables` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `created_at` datetime(3) DEFAULT NULL,
    `updated_at` datetime(3) DEFAULT NULL,
    `deleted_at` datetime(3) DEFAULT NULL,
    `name` varchar(100) NOT NULL,
    `value` text,
    `description` text,
    `type` varchar(50) NOT NULL DEFAULT 'string',
    `status` varchar(20) NOT NULL DEFAULT 'active',
    `app_id` bigint unsigned NOT NULL,
    `tenant_id` bigint unsigned NOT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_variables_name` (`name`),
    KEY `idx_variables_type` (`type`),
    KEY `idx_variables_status` (`status`),
    KEY `idx_variables_app_id` (`app_id`),
    KEY `idx_variables_tenant_id` (`tenant_id`),
    KEY `idx_variables_deleted_at` (`deleted_at`),
    KEY `idx_variables_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='变量表';

-- 云函数表
CREATE TABLE IF NOT EXISTS `cloud_funcs` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `created_at` datetime(3) DEFAULT NULL,
    `updated_at` datetime(3) DEFAULT NULL,
    `deleted_at` datetime(3) DEFAULT NULL,
    `name` varchar(100) NOT NULL,
    `description` text,
    `code` longtext,
    `runtime` varchar(50) NOT NULL DEFAULT 'go1.21',
    `status` varchar(20) NOT NULL DEFAULT 'active',
    `app_id` bigint unsigned NOT NULL,
    `tenant_id` bigint unsigned NOT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_cloud_funcs_name` (`name`),
    KEY `idx_cloud_funcs_runtime` (`runtime`),
    KEY `idx_cloud_funcs_status` (`status`),
    KEY `idx_cloud_funcs_app_id` (`app_id`),
    KEY `idx_cloud_funcs_tenant_id` (`tenant_id`),
    KEY `idx_cloud_funcs_deleted_at` (`deleted_at`),
    KEY `idx_cloud_funcs_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='云函数表';

-- 用户应用关联表
CREATE TABLE IF NOT EXISTS `user_apps` (
    `user_id` bigint unsigned NOT NULL,
    `app_id` bigint unsigned NOT NULL,
    `created_at` datetime(3) DEFAULT NULL,
    `updated_at` datetime(3) DEFAULT NULL,
    PRIMARY KEY (`user_id`,`app_id`),
    KEY `idx_user_apps_app_id` (`app_id`),
    KEY `idx_user_apps_created_at` (`created_at`),
    CONSTRAINT `fk_user_apps_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_user_apps_app_id` FOREIGN KEY (`app_id`) REFERENCES `apps` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户应用关联表';

-- 用户变量关联表
CREATE TABLE IF NOT EXISTS `user_variables` (
    `user_id` bigint unsigned NOT NULL,
    `variable_id` bigint unsigned NOT NULL,
    `created_at` datetime(3) DEFAULT NULL,
    `updated_at` datetime(3) DEFAULT NULL,
    PRIMARY KEY (`user_id`,`variable_id`),
    KEY `idx_user_variables_variable_id` (`variable_id`),
    KEY `idx_user_variables_created_at` (`created_at`),
    CONSTRAINT `fk_user_variables_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_user_variables_variable_id` FOREIGN KEY (`variable_id`) REFERENCES `variables` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户变量关联表';

-- 用户云函数关联表
CREATE TABLE IF NOT EXISTS `user_cloudfuncs` (
    `user_id` bigint unsigned NOT NULL,
    `cloud_func_id` bigint unsigned NOT NULL,
    `created_at` datetime(3) DEFAULT NULL,
    `updated_at` datetime(3) DEFAULT NULL,
    PRIMARY KEY (`user_id`,`cloud_func_id`),
    KEY `idx_user_cloudfuncs_cloud_func_id` (`cloud_func_id`),
    KEY `idx_user_cloudfuncs_created_at` (`created_at`),
    CONSTRAINT `fk_user_cloudfuncs_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_user_cloudfuncs_cloud_func_id` FOREIGN KEY (`cloud_func_id`) REFERENCES `cloud_funcs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户云函数关联表';
