syntax = "v1"

info (
    title: "AMIS CRUD API"
    desc: "定义AMIS CRUD组件的数据结构"
    author: "developer"
    version: "v1.0"
)

 

type (
    // AMIS标准响应结构
    AmisResponse {
        Status int `json:"status"`
        Msg string `json:"msg"`
        Data AmisData `json:"data"`
    }

    // AMIS数据结构
    AmisData {
        Items []interface{} `json:"items"`
        Total int `json:"total"`
        Page int `json:"page"`
    }
    
    // 分页请求参数
    PageRequest {
        Page int `json:"page"`
        PerPage int `json:"perPage"`
    }

    // 路径请求参数
    PathReq {
        Path string `path:"path"`
    }

    // 页面请求参数
    PagePathReq {
        Page string `path:"page"`
    }
)

@server(
    group: systemAmis
)
service AdminService {
    @doc "首页"
    @handler getHome
    get / returns ([]byte)

    @doc "获取AMIS页面配置"
    @handler getAmisPage
    get /pages/:page (PagePathReq) returns ([]byte)

    @doc "通配符路由处理其他静态资源"
    @handler getStaticResource
    get /:path (PathReq) returns ([]byte)
}