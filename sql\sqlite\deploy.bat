@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo SQLite3 多租户认证系统数据库部署脚本
echo ============================================

:: 设置数据库文件路径
set DB_DIR=data
set MASTER_DB=%DB_DIR%\auth_system.db
set TENANT_EXAMPLE_DB=%DB_DIR%\tenant_example.db
set TENANT_TEST_DB=%DB_DIR%\tenant_test.db
set TENANT_DEMO_DB=%DB_DIR%\tenant_demo.db

:: 创建数据目录
if not exist "%DB_DIR%" mkdir "%DB_DIR%"

echo 1. 创建主数据库...
sqlite3 "%MASTER_DB%" < sql\sqlite\master.sql
echo ✓ 主数据库创建完成: %MASTER_DB%

echo.
echo 2. 创建租户数据库...

:: 创建示例租户数据库
sqlite3 "%TENANT_EXAMPLE_DB%" < sql\sqlite\tenant.sql
echo ✓ 示例租户数据库创建完成: %TENANT_EXAMPLE_DB%

:: 创建测试租户数据库
sqlite3 "%TENANT_TEST_DB%" < sql\sqlite\tenant.sql
echo ✓ 测试租户数据库创建完成: %TENANT_TEST_DB%

:: 创建演示租户数据库
sqlite3 "%TENANT_DEMO_DB%" < sql\sqlite\tenant.sql
echo ✓ 演示租户数据库创建完成: %TENANT_DEMO_DB%

echo.
echo 3. 初始化数据...

:: 初始化示例租户数据
sqlite3 "%TENANT_EXAMPLE_DB%" < sql\sqlite\init_data.sql
echo ✓ 示例租户数据初始化完成

:: 初始化测试租户数据
sqlite3 "%TENANT_TEST_DB%" < sql\sqlite\init_data.sql
echo ✓ 测试租户数据初始化完成

echo.
echo 4. 验证部署...

:: 验证主数据库
echo 主数据库表结构:
sqlite3 "%MASTER_DB%" ".tables"

echo 主数据库租户数据:
sqlite3 "%MASTER_DB%" "SELECT id, name, database_name, status FROM tenants;"

:: 验证租户数据库
echo 示例租户数据库表结构:
sqlite3 "%TENANT_EXAMPLE_DB%" ".tables"

echo 示例租户用户数据:
sqlite3 "%TENANT_EXAMPLE_DB%" "SELECT id, username, email, status FROM users LIMIT 5;"

echo 示例租户应用数据:
sqlite3 "%TENANT_EXAMPLE_DB%" "SELECT id, name, app_key, status FROM apps LIMIT 5;"

echo.
echo 5. 创建数据库配置文件...

:: 创建数据库配置文件
echo { > "%DB_DIR%\database_config.json"
echo   "master_database": "%MASTER_DB%", >> "%DB_DIR%\database_config.json"
echo   "tenant_databases": { >> "%DB_DIR%\database_config.json"
echo     "tenant_example": "%TENANT_EXAMPLE_DB%", >> "%DB_DIR%\database_config.json"
echo     "tenant_test": "%TENANT_TEST_DB%", >> "%DB_DIR%\database_config.json"
echo     "tenant_demo": "%TENANT_DEMO_DB%" >> "%DB_DIR%\database_config.json"
echo   }, >> "%DB_DIR%\database_config.json"
echo   "created_at": "%date% %time%", >> "%DB_DIR%\database_config.json"
echo   "version": "1.0.0" >> "%DB_DIR%\database_config.json"
echo } >> "%DB_DIR%\database_config.json"

echo ✓ 数据库配置文件创建完成: %DB_DIR%\database_config.json

echo.
echo ===========================================
echo SQLite3 数据库部署完成！
echo ===========================================
echo.
echo 数据库文件位置:
echo   主数据库: %MASTER_DB%
echo   示例租户: %TENANT_EXAMPLE_DB%
echo   测试租户: %TENANT_TEST_DB%
echo   演示租户: %TENANT_DEMO_DB%
echo.
echo 配置文件: %DB_DIR%\database_config.json
echo.
echo 默认账户:
echo   管理员: <EMAIL> / password
echo   用户1: <EMAIL> / password
echo   用户2: <EMAIL> / password
echo.
echo 应用密钥:
echo   Web应用: app_key_web_001 / app_secret_web_001
echo   移动应用: app_key_mobile_001 / app_secret_mobile_001
echo   API应用: app_key_api_001 / app_secret_api_001
echo.
echo ===========================================

pause
