-- 租户数据库初始化数据示例
-- 注意：此文件需要在创建租户数据库后执行

-- 为示例租户插入用户数据
USE `tenant_example`;

INSERT INTO `users` (`username`, `email`, `password`, `first_name`, `last_name`, `phone`, `status`, `tenant_id`) VALUES
('admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统', '管理员', '13800138000', 'active', 1),
('user1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '张', '三', '13800138001', 'active', 1),
('user2', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '李', '四', '13800138002', 'active', 1);

-- 插入应用数据
INSERT INTO `apps` (`name`, `description`, `app_key`, `app_secret`, `status`, `tenant_id`) VALUES
('Web应用', 'Web端应用', 'app_key_web_001', 'app_secret_web_001', 'active', 1),
('移动应用', '移动端应用', 'app_key_mobile_001', 'app_secret_mobile_001', 'active', 1),
('API应用', 'API接口应用', 'app_key_api_001', 'app_secret_api_001', 'active', 1);

-- 插入变量数据
INSERT INTO `variables` (`name`, `value`, `description`, `type`, `status`, `app_id`, `tenant_id`) VALUES
('api_url', 'https://api.example.com', 'API接口地址', 'string', 'active', 1, 1),
('timeout', '30', '请求超时时间(秒)', 'number', 'active', 1, 1),
('enable_debug', 'true', '是否启用调试模式', 'boolean', 'active', 1, 1),
('feature_flags', '{"new_ui": true, "beta_features": false}', '功能开关配置', 'json', 'active', 1, 1),
('max_retries', '3', '最大重试次数', 'number', 'active', 2, 1),
('theme', 'dark', '应用主题', 'string', 'active', 2, 1),
('push_enabled', 'true', '是否启用推送', 'boolean', 'active', 2, 1),
('rate_limit', '1000', 'API调用限制', 'number', 'active', 3, 1),
('cache_ttl', '3600', '缓存过期时间(秒)', 'number', 'active', 3, 1);

-- 插入云函数数据
INSERT INTO `cloud_funcs` (`name`, `description`, `code`, `runtime`, `status`, `app_id`, `tenant_id`) VALUES
('send_email', '发送邮件函数', 'package main\n\nimport (\n\t\"fmt\"\n\t\"net/smtp\"\n)\n\nfunc SendEmail(to, subject, body string) error {\n\t// 邮件发送逻辑\n\treturn nil\n}', 'go1.21', 'active', 1, 1),
('process_data', '数据处理函数', 'function processData(data) {\n\t// 数据处理逻辑\n\treturn data;\n}', 'nodejs18', 'active', 1, 1),
('generate_report', '生成报表函数', 'def generate_report(data):\n\t# 报表生成逻辑\n\treturn report', 'python3.9', 'active', 2, 1),
('validate_input', '输入验证函数', 'package main\n\nimport \"regexp\"\n\nfunc ValidateEmail(email string) bool {\n\tpattern := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$`\n\tmatch, _ := regexp.MatchString(pattern, email)\n\treturn match\n}', 'go1.21', 'active', 3, 1);

-- 插入用户应用关联数据
INSERT INTO `user_apps` (`user_id`, `app_id`) VALUES
(1, 1), (1, 2), (1, 3), -- 管理员拥有所有应用权限
(2, 1), (2, 2),         -- 用户1拥有Web和移动应用权限
(3, 1);                  -- 用户2只拥有Web应用权限

-- 插入用户变量关联数据
INSERT INTO `user_variables` (`user_id`, `variable_id`) VALUES
(1, 1), (1, 2), (1, 3), (1, 4), -- 管理员关联所有变量
(2, 1), (2, 2),                 -- 用户1关联部分变量
(3, 1);                         -- 用户2关联少量变量

-- 插入用户云函数关联数据
INSERT INTO `user_cloudfuncs` (`user_id`, `cloud_func_id`) VALUES
(1, 1), (1, 2), (1, 3), (1, 4), -- 管理员关联所有云函数
(2, 1), (2, 2),                 -- 用户1关联部分云函数
(3, 1);                         -- 用户2关联少量云函数

-- 为测试租户插入基础数据
USE `tenant_test`;

INSERT INTO `users` (`username`, `email`, `password`, `first_name`, `last_name`, `status`, `tenant_id`) VALUES
('test_admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Test', 'Admin', 'active', 2);

INSERT INTO `apps` (`name`, `description`, `app_key`, `app_secret`, `status`, `tenant_id`) VALUES
('Test App', '测试应用', 'test_app_key_001', 'test_app_secret_001', 'active', 2);

-- 创建存储过程：创建租户数据库
DELIMITER //
CREATE PROCEDURE CreateTenantDatabase(
    IN p_database_name VARCHAR(100),
    IN p_tenant_id BIGINT
)
BEGIN
    DECLARE db_name VARCHAR(100);
    SET db_name = CONCAT('`', p_database_name, '`');
    
    -- 创建数据库
    SET @create_db = CONCAT('CREATE DATABASE IF NOT EXISTS ', db_name, ' CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
    PREPARE stmt FROM @create_db;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- 使用新创建的数据库
    SET @use_db = CONCAT('USE ', db_name);
    PREPARE stmt FROM @use_db;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- 在这里可以执行tenant.sql中的表创建语句
    -- 实际使用时需要读取tenant.sql文件内容并执行
END //
DELIMITER ;

-- 创建视图：租户统计信息
CREATE VIEW tenant_stats AS
SELECT 
    t.id,
    t.name,
    t.database_name,
    t.status,
    t.max_apps,
    t.max_users,
    t.max_variables,
    t.max_cloud_funcs,
    t.current_apps,
    t.current_users,
    t.current_variables,
    t.current_cloud_funcs,
    CASE 
        WHEN t.status = 'active' THEN '运行中'
        WHEN t.status = 'disabled' THEN '已禁用'
        WHEN t.status = 'suspended' THEN '已暂停'
        ELSE '未知'
    END as status_text,
    CONCAT(ROUND(t.current_apps / t.max_apps * 100, 1), '%') as apps_usage,
    CONCAT(ROUND(t.current_users / t.max_users * 100, 1), '%') as users_usage,
    CONCAT(ROUND(t.current_variables / t.max_variables * 100, 1), '%') as variables_usage,
    CONCAT(ROUND(t.current_cloud_funcs / t.max_cloud_funcs * 100, 1), '%') as cloudfuncs_usage,
    t.created_at,
    t.updated_at
FROM tenants t;

-- 创建触发器：更新租户统计信息
DELIMITER //
CREATE TRIGGER update_tenant_stats_after_user_insert
AFTER INSERT ON users
FOR EACH ROW
BEGIN
    UPDATE tenants 
    SET current_users = current_users + 1,
        updated_at = NOW()
    WHERE id = NEW.tenant_id;
END //
DELIMITER ;

DELIMITER //
CREATE TRIGGER update_tenant_stats_after_user_delete
AFTER DELETE ON users
FOR EACH ROW
BEGIN
    UPDATE tenants 
    SET current_users = GREATEST(current_users - 1, 0),
        updated_at = NOW()
    WHERE id = OLD.tenant_id;
END //
DELIMITER ;

DELIMITER //
CREATE TRIGGER update_tenant_stats_after_app_insert
AFTER INSERT ON apps
FOR EACH ROW
BEGIN
    UPDATE tenants 
    SET current_apps = current_apps + 1,
        updated_at = NOW()
    WHERE id = NEW.tenant_id;
END //
DELIMITER ;

DELIMITER //
CREATE TRIGGER update_tenant_stats_after_app_delete
AFTER DELETE ON apps
FOR EACH ROW
BEGIN
    UPDATE tenants 
    SET current_apps = GREATEST(current_apps - 1, 0),
        updated_at = NOW()
    WHERE id = OLD.tenant_id;
END //
DELIMITER ;
