-- 多租户认证系统数据库部署脚本
-- 执行顺序：master.sql -> tenant.sql -> init_data.sql

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 1. 执行主数据库脚本
-- SOURCE master.sql;

-- 2. 为每个租户创建数据库并执行租户数据库脚本
-- 注意：这里需要根据实际租户数据动态执行

-- 示例：为示例租户创建数据库
CREATE DATABASE IF NOT EXISTS `tenant_example` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `tenant_example`;
-- SOURCE tenant.sql;

CREATE DATABASE IF NOT EXISTS `tenant_test` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `tenant_test`;
-- SOURCE tenant.sql;

CREATE DATABASE IF NOT EXISTS `tenant_demo` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `tenant_demo`;
-- SOURCE tenant.sql;

-- 3. 执行初始化数据脚本
-- SOURCE init_data.sql;

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 部署完成提示
SELECT 'Database deployment completed successfully!' as message;

-- 显示数据库信息
SELECT 
    SCHEMA_NAME as database_name,
    DEFAULT_CHARACTER_SET_NAME as charset,
    DEFAULT_COLLATION_NAME as collation
FROM information_schema.SCHEMATA 
WHERE SCHEMA_NAME IN ('auth_system', 'tenant_example', 'tenant_test', 'tenant_demo')
ORDER BY SCHEMA_NAME;

-- 显示表统计信息
SELECT 
    TABLE_SCHEMA as database_name,
    COUNT(*) as table_count
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA IN ('auth_system', 'tenant_example', 'tenant_test', 'tenant_demo')
GROUP BY TABLE_SCHEMA
ORDER BY TABLE_SCHEMA;
