syntax = "v1"

info (
    title:   "用户管理API"
    desc:    "用户的创建、查询、更新、删除等管理功能"
    author:  "auth-system"
    email:   "<EMAIL>"
    version: "v1.0.0"
)

import (
    "types/types.api"
    "types/amis.api"
)

// 创建用户请求
type CreateUserRequest {
    Username  string `json:"username"  validate:"required,min=3,max=50,alphanum"`
    Email     string `json:"email"     validate:"required,email,max=100"`
    Password  string `json:"password"  validate:"required,min=6,max=255"`
    FirstName string `json:"firstName" validate:"max=50"`
    LastName  string `json:"lastName"  validate:"max=50"`
    Phone     string `json:"phone"     validate:"max=20"`
    Status    string `json:"status"    validate:"oneof=active inactive suspended"`
}

// 更新用户请求
type UpdateUserRequest {
    Username  string `json:"username"  validate:"required,min=3,max=50,alphanum"`
    Email     string `json:"email"     validate:"required,email,max=100"`
    FirstName string `json:"firstName" validate:"max=50"`
    LastName  string `json:"lastName"  validate:"max=50"`
    Phone     string `json:"phone"     validate:"max=20"`
    Status    string `json:"status"    validate:"oneof=active inactive suspended"`
}

// 更新用户状态请求
type UpdateUserStatusRequest {
    Status string `json:"status" validate:"required,oneof=active inactive suspended"`
}

// 修改密码请求
type ChangePasswordRequest {
    OldPassword string `json:"oldPassword" validate:"required,min=6,max=255"`
    NewPassword string `json:"newPassword" validate:"required,min=6,max=255"`
}

// 重置密码请求
type ResetPasswordRequest {
    Email string `json:"email" validate:"required,email"`
}

// 用户登录请求
type LoginRequest {
    Username string `json:"username" validate:"required"`
    Password string `json:"password" validate:"required,min=6,max=255"`
}

// 用户登录响应
type LoginResponse {
    UserInfo
    Token string `json:"token"`
}

// 用户列表查询请求
type UserListRequest {
    PageRequest
    Username string `json:"username" validate:"max=50"`   // 用户名过滤
    Email    string `json:"email"    validate:"max=100"`  // 邮箱过滤
    Status   string `json:"status"   validate:"oneof=active inactive suspended"` // 状态过滤
}

// 用户应用关联请求
type UserAppRequest {
    AppIds []uint `json:"appIds" validate:"min=1,max=100"` // 应用ID列表
}

// 用户变量关联请求
type UserVariableRequest {
    VariableIds []uint `json:"variableIds" validate:"min=1,max=1000"` // 变量ID列表
}

// 用户云函数关联请求
type UserCloudFuncRequest {
    CloudFuncIds []uint `json:"cloudFuncIds" validate:"min=1,max=100"` // 云函数ID列表
}

// 用户权限信息
type UserPermissions {
    Apps       []AppInfo       `json:"apps"`       // 用户可访问的应用
    Variables  []VariableInfo  `json:"variables"`  // 用户可访问的变量
    CloudFuncs []CloudFuncInfo `json:"cloudFuncs"` // 用户可访问的云函数
}

// 用户详情响应
type UserDetailResponse {
    UserInfo
    Permissions UserPermissions `json:"permissions"`
}

@server (
    group: user
    prefix: /api/v1
)
service user {
    @doc (
        summary: "创建用户"
        description: "在指定租户下创建新用户"
    )
    @handler CreateUserHandler
    post /tenants/:tenantId/users (CreateUserRequest) returns (AmisResponse)

    @doc (
        summary: "获取用户详情"
        description: "根据用户ID获取用户详细信息"
    )
    @handler GetUserHandler
    get /tenants/:tenantId/users/:id (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "更新用户信息"
        description: "更新用户的基本信息"
    )
    @handler UpdateUserHandler
    put /tenants/:tenantId/users/:id (UpdateUserRequest) returns (AmisResponse)

    @doc (
        summary: "删除用户"
        description: "删除指定用户"
    )
    @handler DeleteUserHandler
    delete /tenants/:tenantId/users/:id (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "获取用户列表"
        description: "分页获取用户列表，支持用户名、邮箱、状态过滤"
    )
    @handler ListUsersHandler
    get /tenants/:tenantId/users (UserListRequest) returns (AmisResponse)

    @doc (
        summary: "用户登录"
        description: "用户登录认证"
    )
    @handler LoginHandler
    post /tenants/:tenantId/users/login (LoginRequest) returns (AmisResponse)

    @doc (
        summary: "用户登出"
        description: "用户登出"
    )
    @handler LogoutHandler
    post /tenants/:tenantId/users/logout (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "修改密码"
        description: "用户修改自己的密码"
    )
    @handler ChangePasswordHandler
    put /tenants/:tenantId/users/:id/password (ChangePasswordRequest) returns (AmisResponse)

    @doc (
        summary: "重置密码"
        description: "通过邮箱重置用户密码"
    )
    @handler ResetPasswordHandler
    post /tenants/:tenantId/users/reset-password (ResetPasswordRequest) returns (AmisResponse)

    @doc (
        summary: "更新用户状态"
        description: "更新用户状态（启用/禁用/暂停）"
    )
    @handler UpdateUserStatusHandler
    put /tenants/:tenantId/users/:id/status (UpdateUserStatusRequest) returns (AmisResponse)

    @doc (
        summary: "分配应用权限"
        description: "为用户分配应用访问权限"
    )
    @handler AssignAppsHandler
    post /tenants/:tenantId/users/:id/apps (UserAppRequest) returns (AmisResponse)

    @doc (
        summary: "移除应用权限"
        description: "移除用户的应用访问权限"
    )
    @handler RemoveAppsHandler
    delete /tenants/:tenantId/users/:id/apps (UserAppRequest) returns (AmisResponse)

    @doc (
        summary: "获取用户应用权限"
        description: "获取用户的应用访问权限列表"
    )
    @handler GetUserAppsHandler
    get /tenants/:tenantId/users/:id/apps (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "分配变量权限"
        description: "为用户分配变量访问权限"
    )
    @handler AssignVariablesHandler
    post /tenants/:tenantId/users/:id/variables (UserVariableRequest) returns (AmisResponse)

    @doc (
        summary: "移除变量权限"
        description: "移除用户的变量访问权限"
    )
    @handler RemoveVariablesHandler
    delete /tenants/:tenantId/users/:id/variables (UserVariableRequest) returns (AmisResponse)

    @doc (
        summary: "获取用户变量权限"
        description: "获取用户的变量访问权限列表"
    )
    @handler GetUserVariablesHandler
    get /tenants/:tenantId/users/:id/variables (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "分配云函数权限"
        description: "为用户分配云函数访问权限"
    )
    @handler AssignCloudFuncsHandler
    post /tenants/:tenantId/users/:id/cloudfuncs (UserCloudFuncRequest) returns (AmisResponse)

    @doc (
        summary: "移除云函数权限"
        description: "移除用户的云函数访问权限"
    )
    @handler RemoveCloudFuncsHandler
    delete /tenants/:tenantId/users/:id/cloudfuncs (UserCloudFuncRequest) returns (AmisResponse)

    @doc (
        summary: "获取用户云函数权限"
        description: "获取用户的云函数访问权限列表"
    )
    @handler GetUserCloudFuncsHandler
    get /tenants/:tenantId/users/:id/cloudfuncs (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "获取用户完整权限信息"
        description: "获取用户的所有权限信息"
    )
    @handler GetUserPermissionsHandler
    get /tenants/:tenantId/users/:id/permissions (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "检查用户名可用性"
        description: "检查用户名是否已被使用"
    )
    @handler CheckUsernameHandler
    get /tenants/:tenantId/users/check-username/:username (AmisResponse) returns (AmisResponse)

    @doc (
        summary: "检查邮箱可用性"
        description: "检查邮箱是否已被使用"
    )
    @handler CheckEmailHandler
    get /tenants/:tenantId/users/check-email/:email (AmisResponse) returns (AmisResponse)
}
